globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/experiments/[id]/review/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/test-layout.tsx":{"*":{"id":"(ssr)/./src/components/layout/test-layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/experiments/page.tsx":{"*":{"id":"(ssr)/./src/app/experiments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/experiments/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/experiments/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/experiments/[id]/review/page.tsx":{"*":{"id":"(ssr)/./src/app/experiments/[id]/review/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\components\\layout\\test-layout.tsx":{"id":"(app-pages-browser)/./src/components/layout/test-layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\styles\\modern-theme.css":{"id":"(app-pages-browser)/./src/styles/modern-theme.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\experiments\\page.tsx":{"id":"(app-pages-browser)/./src/app/experiments/page.tsx","name":"*","chunks":["app/experiments/page","static/chunks/app/experiments/page.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\experiments\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/experiments/[id]/page.tsx","name":"*","chunks":["app/experiments/[id]/page","static/chunks/app/experiments/%5Bid%5D/page.js"],"async":false},"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\experiments\\[id]\\review\\page.tsx":{"id":"(app-pages-browser)/./src/app/experiments/[id]/review/page.tsx","name":"*","chunks":["app/experiments/[id]/review/page","static/chunks/app/experiments/%5Bid%5D/review/page.js"],"async":false}},"entryCSSFiles":{"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\page":[],"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\layout":["static/css/app/layout.css"],"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\not-found":[],"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\experiments\\page":[],"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\experiments\\[id]\\page":[],"E:\\01-main\\framework\\experiment-manager\\frontend\\src\\app\\experiments\\[id]\\review\\page":[]}}