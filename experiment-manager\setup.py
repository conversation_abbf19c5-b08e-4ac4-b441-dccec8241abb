"""
ExpManager - 实验管理系统Python客户端库
基于"三阶段十二步契约流程"的科学实验管理工具
"""

from setuptools import setup, find_packages
import os
from pathlib import Path

# 读取版本信息
def get_version():
    version_file = Path(__file__).parent / "expmanager" / "__init__.py"
    with open(version_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.startswith('__version__'):
                return line.split('=')[1].strip().strip('"\'')
    return "1.0.0"

# 读取README
def get_long_description():
    readme_file = Path(__file__).parent / "README.md"
    if readme_file.exists():
        with open(readme_file, 'r', encoding='utf-8') as f:
            return f.read()
    return ""

# 读取依赖
def get_requirements():
    requirements_file = Path(__file__).parent / "requirements.txt"
    if requirements_file.exists():
        with open(requirements_file, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return [
        "requests>=2.25.0",
        "fastapi>=0.68.0",
        "uvicorn>=0.15.0",
        "sqlalchemy>=1.4.0",
        "pydantic>=1.8.0"
    ]

setup(
    name="expmanager",
    version=get_version(),
    author="Sean (deepractice.ai)",
    author_email="<EMAIL>",
    description="科学实验管理工具 - 将实验从艺术变成工程",
    long_description=get_long_description(),
    long_description_content_type="text/markdown",
    url="https://github.com/deepractice/expmanager",
    project_urls={
        "Bug Tracker": "https://github.com/deepractice/expmanager/issues",
        "Documentation": "https://github.com/deepractice/expmanager/wiki",
        "Source Code": "https://github.com/deepractice/expmanager",
    },
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "Intended Audience :: Developers",
        "Topic :: Scientific/Engineering",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=get_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ],
        "server": [
            "fastapi>=0.68.0",
            "uvicorn>=0.15.0",
            "sqlalchemy>=1.4.0",
            "pydantic>=1.8.0",
        ],
        "full": [
            "fastapi>=0.68.0",
            "uvicorn>=0.15.0", 
            "sqlalchemy>=1.4.0",
            "pydantic>=1.8.0",
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "mypy>=0.800",
        ]
    },
    entry_points={
        "console_scripts": [
            "expmanager=expmanager.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "expmanager": [
            "templates/*.json",
            "static/*",
        ],
    },
    keywords=[
        "experiment", "management", "research", "science", 
        "ml", "machine-learning", "data-science", "reproducibility"
    ],
    zip_safe=False,
)
