[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "expmanager"
dynamic = ["version"]
description = "科学实验管理工具 - 将实验从艺术变成工程"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
keywords = [
    "experiment", "management", "research", "science", 
    "ml", "machine-learning", "data-science", "reproducibility"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Developers",
    "Topic :: Scientific/Engineering",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
requires-python = ">=3.8"
dependencies = [
    "requests>=2.25.0",
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0",
    "sqlalchemy>=1.4.0",
    "pydantic>=1.8.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "flake8>=3.8",
    "mypy>=0.800",
]
server = [
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0",
    "sqlalchemy>=1.4.0",
    "pydantic>=1.8.0",
]
full = [
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0", 
    "sqlalchemy>=1.4.0",
    "pydantic>=1.8.0",
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "flake8>=3.8",
    "mypy>=0.800",
]

[project.urls]
Homepage = "https://github.com/deepractice/expmanager"
Documentation = "https://github.com/deepractice/expmanager/wiki"
Repository = "https://github.com/deepractice/expmanager.git"
"Bug Tracker" = "https://github.com/deepractice/expmanager/issues"

[project.scripts]
expmanager = "expmanager.cli:main"

[tool.setuptools]
packages = ["expmanager"]

[tool.setuptools.dynamic]
version = {attr = "expmanager.__version__"}

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --cov=expmanager --cov-report=term-missing"
