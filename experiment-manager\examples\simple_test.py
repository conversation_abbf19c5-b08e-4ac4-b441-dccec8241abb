"""
ExpManager简单测试示例
不依赖外部库的基础功能测试
"""

import sys
import os
import time
import random

# 添加父目录到Python路径（用于本地开发）
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入expmanager库
try:
    from expmanager import start_experiment, configure
    print("✅ ExpManager库导入成功")
except ImportError as e:
    print(f"❌ ExpManager库导入失败: {e}")
    print("💡 解决方案:")
    print("   1. 在experiment-manager目录下运行: pip install -e .")
    print("   2. 或者运行: pip install expmanager")
    print("   3. 或者确保Python路径正确")
    sys.exit(1)


def simple_experiment_test():
    """简单实验测试"""
    print("🧪 开始简单实验测试...")
    
    # 配置ExpManager
    configure(
        verbose=True,
        auto_open_browser=True,
        fallback_mode=True  # 启用降级模式，即使服务器不可用也能运行
    )
    
    # 启动实验
    exp_id = start_experiment(
        name="ExpManager功能测试",
        hypothesis="ExpManager库能够正常工作并提供实验管理功能",
        description="测试ExpManager库的基本功能，包括实验注册和自动复盘",
        tags=["test", "basic-functionality", "library-test"]
    )
    
    print(f"✅ 实验已启动，ID: {exp_id}")
    
    # 模拟实验过程
    print("📊 模拟实验执行...")
    
    results = {}
    methods = ["方法A", "方法B", "方法C"]
    
    for method in methods:
        print(f"🔬 测试 {method}...")
        
        # 模拟实验时间
        time.sleep(1)
        
        # 模拟实验结果
        score = random.uniform(0.7, 0.95)
        results[method] = score
        
        print(f"   {method} 得分: {score:.4f}")
    
    # 分析结果
    best_method = max(results, key=results.get)
    best_score = results[best_method]
    
    print(f"\n🎯 实验结果分析:")
    print(f"   最佳方法: {best_method}")
    print(f"   最高得分: {best_score:.4f}")
    
    # 验证假设
    if best_score > 0.8:
        print("✅ 假设验证成功: ExpManager库功能正常")
    else:
        print("❌ 假设验证失败: 需要进一步检查")
    
    print(f"\n🔚 实验完成！")
    print("📊 实验结束时将自动打开复盘页面（如果服务器可用）")
    
    return exp_id


def configuration_test():
    """配置功能测试"""
    print("\n🔧 配置功能测试...")
    
    # 测试不同配置
    configure(
        verbose=False,
        auto_open_browser=False,
        fallback_mode=True
    )
    
    exp_id = start_experiment(
        name="配置测试实验",
        hypothesis="不同配置下ExpManager仍能正常工作"
    )
    
    print(f"✅ 配置测试完成，实验ID: {exp_id}")
    
    return exp_id


def error_handling_test():
    """错误处理测试"""
    print("\n🛡️ 错误处理测试...")
    
    try:
        # 测试降级模式
        configure(
            api_url="http://nonexistent-server:9999",  # 不存在的服务器
            fallback_mode=True,
            verbose=True
        )
        
        exp_id = start_experiment(
            name="错误处理测试",
            hypothesis="即使服务器不可用，ExpManager也能正常工作"
        )
        
        print(f"✅ 错误处理测试通过，降级模式工作正常")
        print(f"   临时实验ID: {exp_id}")
        
        return exp_id
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return None


def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 ExpManager库功能测试")
    print("=" * 60)
    
    try:
        # 基础功能测试
        exp_id1 = simple_experiment_test()
        
        # 配置功能测试
        exp_id2 = configuration_test()
        
        # 错误处理测试
        exp_id3 = error_handling_test()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print(f"📋 测试结果:")
        print(f"   基础功能测试: {'✅ 通过' if exp_id1 else '❌ 失败'}")
        print(f"   配置功能测试: {'✅ 通过' if exp_id2 else '❌ 失败'}")
        print(f"   错误处理测试: {'✅ 通过' if exp_id3 else '❌ 失败'}")
        print("=" * 60)
        
        if all([exp_id1, exp_id2, exp_id3]):
            print("🎊 恭喜！ExpManager库安装和配置成功！")
            print("📚 现在可以在你的项目中使用ExpManager了")
        else:
            print("⚠️ 部分测试失败，请检查安装和配置")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
