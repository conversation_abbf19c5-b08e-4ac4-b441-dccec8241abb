#!/usr/bin/env python3
"""
复盘页面问题诊断脚本
检查后端API和实验数据状态
"""

import requests
import json
import sys
from datetime import datetime


def check_backend_health():
    """检查后端健康状态"""
    print("🔍 检查后端服务状态...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务异常，状态码: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到后端服务: {e}")
        return False


def check_experiment_exists(experiment_id):
    """检查实验是否存在"""
    print(f"🔍 检查实验 {experiment_id} 是否存在...")
    
    try:
        response = requests.get(f"http://localhost:8000/api/experiments/{experiment_id}", timeout=5)
        if response.status_code == 200:
            experiment = response.json()
            print("✅ 实验存在")
            print(f"   实验名称: {experiment.get('name', 'N/A')}")
            print(f"   实验状态: {experiment.get('status', 'N/A')}")
            print(f"   创建时间: {experiment.get('created_at', 'N/A')}")
            print(f"   完成时间: {experiment.get('completed_at', 'N/A')}")
            return experiment
        elif response.status_code == 404:
            print("❌ 实验不存在")
            return None
        else:
            print(f"❌ 获取实验信息失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return None


def check_experiment_status(experiment):
    """检查实验状态"""
    if not experiment:
        return False
    
    print("🔍 检查实验状态...")
    
    status = experiment.get('status')
    if status == 'completed':
        print("✅ 实验状态为已完成，可以进行复盘")
        return True
    else:
        print(f"⚠️ 实验状态为 '{status}'，需要完成状态才能复盘")
        return False


def update_experiment_status(experiment_id):
    """更新实验状态为已完成"""
    print(f"🔧 尝试更新实验 {experiment_id} 状态为已完成...")
    
    try:
        update_data = {
            "status": "completed",
            "completed_at": datetime.now().isoformat()
        }
        
        response = requests.patch(
            f"http://localhost:8000/api/experiments/{experiment_id}",
            json=update_data,
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ 实验状态更新成功")
            return True
        else:
            print(f"❌ 更新失败，状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 更新请求失败: {e}")
        return False


def check_frontend_connectivity():
    """检查前端服务连接性"""
    print("🔍 检查前端服务...")
    
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code in [200, 404]:  # 404也表示服务在运行
            print("✅ 前端服务正常运行")
            return True
        else:
            print(f"⚠️ 前端服务状态异常，状态码: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到前端服务: {e}")
        print("💡 请确保前端服务已启动: npm run dev")
        return False


def list_all_experiments():
    """列出所有实验"""
    print("📋 列出所有实验...")
    
    try:
        response = requests.get("http://localhost:8000/api/experiments", timeout=5)
        if response.status_code == 200:
            data = response.json()
            experiments = data.get('experiments', [])
            
            if experiments:
                print(f"✅ 找到 {len(experiments)} 个实验:")
                for exp in experiments:
                    print(f"   ID: {exp.get('id', 'N/A')}")
                    print(f"   名称: {exp.get('name', 'N/A')}")
                    print(f"   状态: {exp.get('status', 'N/A')}")
                    print(f"   创建时间: {exp.get('created_at', 'N/A')}")
                    print("   ---")
                return experiments
            else:
                print("⚠️ 没有找到任何实验")
                return []
        else:
            print(f"❌ 获取实验列表失败，状态码: {response.status_code}")
            return []
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return []


def main():
    """主诊断流程"""
    print("=" * 60)
    print("🔧 复盘页面问题诊断工具")
    print("=" * 60)
    
    # 获取实验ID
    if len(sys.argv) > 1:
        experiment_id = sys.argv[1]
    else:
        experiment_id = input("请输入实验ID (或按回车查看所有实验): ").strip()
    
    # 检查后端服务
    if not check_backend_health():
        print("\n💡 解决方案:")
        print("   1. 启动后端服务: python start.py")
        print("   2. 或手动启动: cd backend && python -m uvicorn main:app --reload")
        return
    
    # 如果没有提供实验ID，列出所有实验
    if not experiment_id:
        experiments = list_all_experiments()
        if experiments:
            print("\n💡 选择一个实验ID重新运行此脚本:")
            print(f"   python {sys.argv[0]} <experiment_id>")
        return
    
    # 检查特定实验
    print(f"\n🎯 诊断实验: {experiment_id}")
    
    # 检查实验是否存在
    experiment = check_experiment_exists(experiment_id)
    if not experiment:
        print("\n💡 可能的原因:")
        print("   1. 实验ID不正确")
        print("   2. 实验未正确保存到数据库")
        print("   3. 数据库连接问题")
        return
    
    # 检查实验状态
    if not check_experiment_status(experiment):
        print("\n💡 解决方案:")
        print("   1. 自动更新实验状态为已完成")
        print("   2. 手动在数据库中更新状态")
        
        choice = input("\n是否自动更新实验状态为已完成? (y/N): ").strip().lower()
        if choice == 'y':
            if update_experiment_status(experiment_id):
                print("✅ 状态更新成功，现在可以访问复盘页面了")
            else:
                print("❌ 状态更新失败")
                return
        else:
            print("⚠️ 实验状态未更新，复盘页面将显示'实验尚未完成'")
            return
    
    # 检查前端服务
    check_frontend_connectivity()
    
    # 生成复盘页面链接
    review_url = f"http://localhost:3000/experiments/{experiment_id}/review"
    
    print("\n" + "=" * 60)
    print("🎉 诊断完成！")
    print("=" * 60)
    print("📊 复盘页面链接:")
    print(f"   {review_url}")
    print("\n💡 如果页面仍然空白，请检查:")
    print("   1. 浏览器控制台是否有JavaScript错误")
    print("   2. 网络请求是否正常")
    print("   3. 前端服务是否正常运行")
    print("=" * 60)


if __name__ == "__main__":
    main()
