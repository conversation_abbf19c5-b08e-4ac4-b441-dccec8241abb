#!/bin/bash

echo "============================================================"
echo "🚀 ExpManager 服务启动脚本 (Linux/Mac)"
echo "============================================================"

# 检查当前目录
if [ ! -d "backend" ]; then
    echo "❌ 后端目录不存在，请在experiment-manager目录下运行此脚本"
    exit 1
fi

if [ ! -d "frontend" ]; then
    echo "❌ 前端目录不存在，请在experiment-manager目录下运行此脚本"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请从 https://nodejs.org/ 下载安装"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装"
    exit 1
fi

echo "✅ 环境检查通过"

# 安装前端依赖
echo "📦 检查前端依赖..."
cd frontend
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 前端依赖安装失败"
        exit 1
    fi
fi
cd ..

# 创建日志目录
mkdir -p logs

# 启动后端服务
echo "🚀 启动后端服务..."
cd backend
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
cd ..

# 等待2秒
sleep 2

# 启动前端服务
echo "🎨 启动前端服务..."
cd frontend
npm run dev -- --port 3000 > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
echo "🔍 检查服务状态..."

# 检查后端
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ 后端服务正常"
else
    echo "⚠️ 后端服务可能未完全启动"
fi

# 检查前端
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ 前端服务正常"
else
    echo "⚠️ 前端服务可能未完全启动"
fi

echo "============================================================"
echo "🎉 服务启动完成！"
echo "============================================================"
echo "📊 访问地址:"
echo "   前端界面: http://localhost:3000"
echo "   后端API: http://localhost:8000"
echo "   API文档: http://localhost:8000/docs"
echo ""
echo "💡 现在可以运行实验脚本:"
echo "   python examples/simple_test.py"
echo ""
echo "💡 复盘页面现在应该可以正常显示了！"
echo ""
echo "📋 进程ID:"
echo "   后端PID: $BACKEND_PID"
echo "   前端PID: $FRONTEND_PID"
echo ""
echo "🛑 停止服务:"
echo "   kill $BACKEND_PID $FRONTEND_PID"
echo "============================================================"

# 创建停止脚本
cat > stop_services.sh << EOF
#!/bin/bash
echo "🛑 停止ExpManager服务..."
kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
echo "✅ 服务已停止"
EOF

chmod +x stop_services.sh

# 打开浏览器 (如果可用)
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:3000
elif command -v open &> /dev/null; then
    open http://localhost:3000
fi

echo "⚠️ 按 Ctrl+C 或运行 ./stop_services.sh 停止服务"

# 等待用户中断
trap "echo ''; echo '🛑 正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo '✅ 服务已停止'; exit 0" INT

# 保持脚本运行
while true; do
    sleep 1
done
