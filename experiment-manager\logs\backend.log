INFO:     Will watch for changes in these directories: ['/mnt/e/01-main/framework/experiment-manager/backend']
INFO:     <PERSON>vic<PERSON> running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [848] using WatchFiles
INFO:     Started server process [856]
INFO:     Waiting for application startup.
2025-07-31 22:25:45,139 - main - INFO - Starting Experiment Manager API...
2025-07-31 22:25:45,139 - main - INFO - Database URL: sqlite:///./data/experiments.db
2025-07-31 22:25:45,151 - main - INFO - Database tables created successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:43592 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57918 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57918 - "POST /api/experiments HTTP/1.1" 307 Temporary Redirect
2025-07-31 22:26:16,422 - crud.experiment - INFO - Created experiment: 9idN6GZnF9impmTxzVQhBJ
2025-07-31 22:26:16,423 - api.experiments - INFO - Created experiment via API: 9idN6GZnF9impmTxzVQhBJ
INFO:     127.0.0.1:57918 - "POST /api/experiments/ HTTP/1.1" 201 Created
INFO:     127.0.0.1:49286 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:49286 - "POST /api/experiments HTTP/1.1" 307 Temporary Redirect
2025-07-31 22:26:21,591 - crud.experiment - INFO - Created experiment: h2oubqMqJgGKRHM5WfrWX6
2025-07-31 22:26:21,591 - api.experiments - INFO - Created experiment via API: h2oubqMqJgGKRHM5WfrWX6
INFO:     127.0.0.1:49286 - "POST /api/experiments/ HTTP/1.1" 201 Created
INFO:     127.0.0.1:49288 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:49288 - "POST /api/experiments HTTP/1.1" 307 Temporary Redirect
2025-07-31 22:26:23,737 - crud.experiment - INFO - Created experiment: J388nMDGUyTeLvspstjAJc
2025-07-31 22:26:23,737 - api.experiments - INFO - Created experiment via API: J388nMDGUyTeLvspstjAJc
INFO:     127.0.0.1:49288 - "POST /api/experiments/ HTTP/1.1" 201 Created
2025-07-31 22:26:23,799 - crud.experiment - INFO - Updated experiment: J388nMDGUyTeLvspstjAJc
2025-07-31 22:26:23,799 - api.experiments - INFO - Updated experiment via API: J388nMDGUyTeLvspstjAJc
INFO:     127.0.0.1:49288 - "PATCH /api/experiments/J388nMDGUyTeLvspstjAJc HTTP/1.1" 200 OK
2025-07-31 22:26:23,971 - crud.experiment - INFO - Updated experiment: h2oubqMqJgGKRHM5WfrWX6
2025-07-31 22:26:23,971 - api.experiments - INFO - Updated experiment via API: h2oubqMqJgGKRHM5WfrWX6
INFO:     127.0.0.1:49286 - "PATCH /api/experiments/h2oubqMqJgGKRHM5WfrWX6 HTTP/1.1" 200 OK
2025-07-31 22:26:26,052 - crud.experiment - INFO - Updated experiment: 9idN6GZnF9impmTxzVQhBJ
2025-07-31 22:26:26,052 - api.experiments - INFO - Updated experiment via API: 9idN6GZnF9impmTxzVQhBJ
INFO:     127.0.0.1:49302 - "PATCH /api/experiments/9idN6GZnF9impmTxzVQhBJ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "OPTIONS /api/experiments/h2oubqMqJgGKRHM5WfrWX6 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "OPTIONS /api/experiments/J388nMDGUyTeLvspstjAJc HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "OPTIONS /api/experiments/h2oubqMqJgGKRHM5WfrWX6 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "OPTIONS /api/experiments/J388nMDGUyTeLvspstjAJc HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "OPTIONS /api/experiments/9idN6GZnF9impmTxzVQhBJ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "OPTIONS /api/experiments/9idN6GZnF9impmTxzVQhBJ HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "GET /api/experiments/h2oubqMqJgGKRHM5WfrWX6 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "GET /api/experiments/9idN6GZnF9impmTxzVQhBJ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/h2oubqMqJgGKRHM5WfrWX6 HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/9idN6GZnF9impmTxzVQhBJ HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "GET /api/experiments/h2oubqMqJgGKRHM5WfrWX6/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "GET /api/experiments/9idN6GZnF9impmTxzVQhBJ/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/h2oubqMqJgGKRHM5WfrWX6/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/9idN6GZnF9impmTxzVQhBJ/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "OPTIONS /api/experiments HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "OPTIONS /api/experiments HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:59998 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:59998 - "OPTIONS /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "OPTIONS /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:60006 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:60006 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:60006 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:60006 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:60006 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:60006 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:60006 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:60006 - "GET /api/experiments HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:60006 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:59998 - "GET /api/experiments/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:60010 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:60012 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:60012 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:60010 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:60010 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc/analysis HTTP/1.1" 200 OK
INFO:     127.0.0.1:60012 - "GET /api/experiments/J388nMDGUyTeLvspstjAJc/analysis HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-31 22:27:18,157 - main - INFO - Shutting down Experiment Manager API...
INFO:     Application shutdown complete.
INFO:     Finished server process [856]
INFO:     Stopping reloader process [848]
