#!/usr/bin/env python3
"""
ExpManager包构建和发布脚本
基于奥卡姆剃刀原则的简洁发布流程
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(cmd, cwd=None, check=True):
    """运行命令并处理输出"""
    print(f"🔧 执行: {cmd}")
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def clean_build():
    """清理构建文件"""
    print("🧹 清理构建文件...")
    
    dirs_to_clean = [
        "build",
        "dist", 
        "*.egg-info",
        "__pycache__",
        ".pytest_cache",
        ".coverage"
    ]
    
    for pattern in dirs_to_clean:
        if "*" in pattern:
            # 使用glob模式
            for path in Path(".").glob(f"**/{pattern}"):
                if path.is_dir():
                    shutil.rmtree(path)
                    print(f"   删除目录: {path}")
                else:
                    path.unlink()
                    print(f"   删除文件: {path}")
        else:
            path = Path(pattern)
            if path.exists():
                if path.is_dir():
                    shutil.rmtree(path)
                    print(f"   删除目录: {path}")
                else:
                    path.unlink()
                    print(f"   删除文件: {path}")


def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    
    # 检查是否有pytest
    try:
        subprocess.check_call(["python", "-m", "pytest", "--version"], 
                            stdout=subprocess.DEVNULL)
    except subprocess.CalledProcessError:
        print("⚠️ pytest未安装，跳过测试")
        return True
    
    # 运行测试
    result = run_command("python -m pytest tests/ -v", check=False)
    if result.returncode != 0:
        print("❌ 测试失败")
        return False
    
    print("✅ 测试通过")
    return True


def check_code_quality():
    """检查代码质量"""
    print("🔍 检查代码质量...")
    
    # 检查black格式化
    try:
        result = run_command("python -m black --check expmanager/", check=False)
        if result.returncode != 0:
            print("⚠️ 代码格式不符合black标准，正在自动格式化...")
            run_command("python -m black expmanager/")
    except:
        print("⚠️ black未安装，跳过格式检查")
    
    # 检查flake8
    try:
        result = run_command("python -m flake8 expmanager/ --max-line-length=88", check=False)
        if result.returncode != 0:
            print("⚠️ 代码风格检查发现问题，请手动修复")
    except:
        print("⚠️ flake8未安装，跳过风格检查")


def build_package():
    """构建包"""
    print("📦 构建包...")
    
    # 确保构建工具已安装
    run_command("python -m pip install --upgrade build twine")
    
    # 构建包
    run_command("python -m build")
    
    print("✅ 包构建完成")


def check_package():
    """检查包"""
    print("🔍 检查包...")
    
    # 检查包内容
    run_command("python -m twine check dist/*")
    
    print("✅ 包检查通过")


def publish_to_test_pypi():
    """发布到测试PyPI"""
    print("🚀 发布到测试PyPI...")
    
    # 发布到测试PyPI
    run_command("python -m twine upload --repository testpypi dist/*")
    
    print("✅ 已发布到测试PyPI")
    print("🔗 测试安装: pip install --index-url https://test.pypi.org/simple/ expmanager")


def publish_to_pypi():
    """发布到正式PyPI"""
    print("🚀 发布到正式PyPI...")
    
    # 确认发布
    response = input("确认发布到正式PyPI? (y/N): ")
    if response.lower() != 'y':
        print("❌ 发布已取消")
        return
    
    # 发布到正式PyPI
    run_command("python -m twine upload dist/*")
    
    print("✅ 已发布到正式PyPI")
    print("🔗 安装命令: pip install expmanager")


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 ExpManager包构建和发布工具")
    print("=" * 60)
    
    # 检查当前目录
    if not Path("setup.py").exists() and not Path("pyproject.toml").exists():
        print("❌ 未找到setup.py或pyproject.toml文件")
        print("请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="ExpManager包构建和发布工具")
    parser.add_argument("--clean", action="store_true", help="清理构建文件")
    parser.add_argument("--test", action="store_true", help="运行测试")
    parser.add_argument("--check", action="store_true", help="检查代码质量")
    parser.add_argument("--build", action="store_true", help="构建包")
    parser.add_argument("--test-publish", action="store_true", help="发布到测试PyPI")
    parser.add_argument("--publish", action="store_true", help="发布到正式PyPI")
    parser.add_argument("--all", action="store_true", help="执行完整流程（除正式发布）")
    
    args = parser.parse_args()
    
    try:
        if args.all or not any(vars(args).values()):
            # 完整流程
            clean_build()
            check_code_quality()
            if not run_tests():
                print("❌ 测试失败，停止构建")
                sys.exit(1)
            build_package()
            check_package()
            print("\n🎉 构建完成！")
            print("💡 使用 --test-publish 发布到测试PyPI")
            print("💡 使用 --publish 发布到正式PyPI")
        else:
            # 单独执行
            if args.clean:
                clean_build()
            if args.check:
                check_code_quality()
            if args.test:
                if not run_tests():
                    sys.exit(1)
            if args.build:
                build_package()
                check_package()
            if args.test_publish:
                publish_to_test_pypi()
            if args.publish:
                publish_to_pypi()
        
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
