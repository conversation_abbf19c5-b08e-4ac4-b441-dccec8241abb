# 🔧 修复复盘页面空白问题

> 解决 `http://localhost:3000/experiments/xxx/review` 显示空白的问题

## ✅ **问题确认**

你的ExpManager库工作正常！🎉
- ✅ 实验注册成功 (ID: `V7oZS6VyD4dADF26DvncV4`)
- ✅ 后端API正常工作 (端口8000)
- ❌ 前端服务未启动 (端口3000) ← 这是问题所在

## 🚀 **立即解决方案**

### **方案1：一键启动所有服务（推荐）**

```bash
# 在experiment-manager目录下运行
python start_services.py
```

这个脚本会：
- 🚀 自动启动后端服务 (端口8000)
- 🎨 自动启动前端服务 (端口3000)
- 🌐 自动打开浏览器
- ⏳ 等待服务就绪

### **方案2：使用原有启动脚本**

```bash
# Windows
start.bat

# Linux/Mac
./start.sh

# 或跨平台
python start.py
```

### **方案3：手动启动前端**

```bash
# 进入前端目录
cd frontend

# 安装依赖（首次运行）
npm install

# 启动前端服务
npm run dev -- --port 3000
```

## 🔍 **验证解决方案**

启动服务后：

1. **检查服务状态**:
   - 后端: http://localhost:8000/health
   - 前端: http://localhost:3000

2. **重新运行实验**:
   ```bash
   python examples/simple_test.py
   ```

3. **复盘页面应该正常显示**:
   - 实验概览
   - 数据分析
   - 智能洞察
   - 团队协作功能

## 🛠️ **如果Node.js未安装**

前端需要Node.js环境：

1. **下载安装Node.js**: https://nodejs.org/
2. **验证安装**:
   ```bash
   node --version
   npm --version
   ```
3. **重新运行启动脚本**

## 💡 **临时解决方案（仅API模式）**

如果不想安装Node.js，可以配置ExpManager仅使用API：

```python
from expmanager import configure

configure(
    auto_open_browser=False,  # 不自动打开浏览器
    verbose=True
)

# 实验数据可通过API访问
# http://localhost:8000/api/experiments/{experiment_id}
```

## 🎯 **完整工作流程**

1. **启动服务**:
   ```bash
   python start_services.py
   ```

2. **运行实验**:
   ```python
   from expmanager import start_experiment
   
   exp_id = start_experiment(
       name="我的实验",
       hypothesis="我的假设"
   )
   
   # 你的实验代码...
   ```

3. **自动复盘**: 脚本结束时自动打开复盘页面

4. **Web界面功能**:
   - 📊 实验概览和时间线
   - 📈 数据分析和可视化
   - 🧠 AI驱动的智能洞察
   - 💬 团队评论和协作
   - 📁 数据导出 (JSON/CSV/PDF)

## 🔧 **故障排除**

### 端口被占用
```bash
# 检查端口占用
netstat -an | grep 3000
netstat -an | grep 8000

# 杀死占用进程（Windows）
taskkill /f /pid <PID>

# 杀死占用进程（Linux/Mac）
kill -9 <PID>
```

### 权限问题
```bash
# 使用管理员权限运行
sudo python start_services.py  # Linux/Mac

# 或修改端口
python start_services.py --frontend-port 3001
```

### 防火墙问题
- 确保端口3000和8000未被防火墙阻止
- 或在防火墙中添加例外

## 🎉 **成功标志**

当看到以下信息时，表示一切正常：

```
🎉 服务启动完成！
📊 访问地址:
   前端界面: http://localhost:3000
   后端API: http://localhost:8000
   API文档: http://localhost:8000/docs

💡 现在可以重新运行你的实验脚本
   复盘页面将正常显示
```

## 📞 **仍需帮助？**

如果问题仍然存在：

1. **查看详细日志**: 运行启动脚本查看错误信息
2. **检查系统要求**: Python 3.8+, Node.js 16+
3. **联系支持**: 
   - GitHub Issues: https://github.com/deepractice/expmanager/issues
   - 邮箱: <EMAIL>

---

<div align="center">

**🎊 ExpManager让实验管理触手可及！**

从"艺术"到"工程"的完美转换

</div>
