#!/usr/bin/env python3
"""
ExpManager快速启动脚本
修复版本 - 正确处理前端目录结构
"""

import os
import sys
import time
import subprocess
import threading
import signal
from pathlib import Path


def print_colored(message, color_code=94):
    """打印彩色消息"""
    print(f"\033[{color_code}m{message}\033[0m")


def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                   🧪 ExpManager 快速启动                      ║
║                                                              ║
║  🚀 自动启动前后端服务                                         ║
║  📊 完整的实验管理和复盘功能                                    ║
║  🔧 修复版 - 解决路径问题                                      ║
╚══════════════════════════════════════════════════════════════╝
    """
    print_colored(banner, 95)


def check_requirements():
    """检查环境要求"""
    print_colored("🔍 检查环境要求...", 96)
    
    # 检查Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print_colored(f"❌ Python版本过低: {python_version.major}.{python_version.minor}", 91)
        print_colored("需要Python 3.8或更高版本", 91)
        return False
    
    print_colored(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}", 92)
    
    # 检查目录结构
    backend_dir = Path("backend")
    frontend_dir = Path("frontend")
    
    if not backend_dir.exists():
        print_colored("❌ 后端目录不存在: backend/", 91)
        return False
    
    if not frontend_dir.exists():
        print_colored("❌ 前端目录不存在: frontend/", 91)
        return False
    
    if not (frontend_dir / "package.json").exists():
        print_colored("❌ 前端package.json不存在", 91)
        return False
    
    print_colored("✅ 目录结构正确", 92)
    
    # 检查Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True, check=True)
        node_version = result.stdout.strip()
        print_colored(f"✅ Node.js版本: {node_version}", 92)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_colored("❌ Node.js未安装", 91)
        print_colored("请从 https://nodejs.org/ 下载安装Node.js", 93)
        return False
    
    # 检查npm
    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True, check=True)
        npm_version = result.stdout.strip()
        print_colored(f"✅ npm版本: {npm_version}", 92)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_colored("❌ npm未安装", 91)
        return False
    
    return True


def install_backend_deps():
    """安装后端依赖"""
    print_colored("📦 检查后端依赖...", 96)
    
    requirements_file = Path("backend/requirements.txt")
    if requirements_file.exists():
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print_colored("✅ 后端依赖已安装", 92)
        except subprocess.CalledProcessError:
            print_colored("⚠️ 后端依赖安装失败，继续启动...", 93)
    else:
        print_colored("⚠️ 未找到requirements.txt，跳过依赖安装", 93)


def install_frontend_deps():
    """安装前端依赖"""
    print_colored("📦 检查前端依赖...", 96)
    
    frontend_dir = Path("frontend")
    node_modules = frontend_dir / "node_modules"
    
    if not node_modules.exists():
        print_colored("📦 安装前端依赖...", 96)
        try:
            subprocess.check_call(["npm", "install"], cwd=frontend_dir)
            print_colored("✅ 前端依赖已安装", 92)
        except subprocess.CalledProcessError as e:
            print_colored(f"❌ 前端依赖安装失败: {e}", 91)
            return False
    else:
        print_colored("✅ 前端依赖已存在", 92)
    
    return True


def start_backend():
    """启动后端服务"""
    print_colored("🚀 启动后端服务...", 96)
    
    backend_dir = Path("backend")
    
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "main:app",
            "--host", "0.0.0.0", "--port", "8000", "--reload"
        ], cwd=backend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print_colored("✅ 后端服务启动中... (http://localhost:8000)", 92)
        return process
        
    except Exception as e:
        print_colored(f"❌ 后端启动失败: {e}", 91)
        return None


def start_frontend():
    """启动前端服务"""
    print_colored("🎨 启动前端服务...", 96)
    
    frontend_dir = Path("frontend")
    
    try:
        process = subprocess.Popen([
            "npm", "run", "dev", "--", "--port", "3000"
        ], cwd=frontend_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print_colored("✅ 前端服务启动中... (http://localhost:3000)", 92)
        return process
        
    except Exception as e:
        print_colored(f"❌ 前端启动失败: {e}", 91)
        return None


def wait_for_services():
    """等待服务启动"""
    print_colored("⏳ 等待服务启动...", 96)
    
    import requests
    
    # 等待后端
    for i in range(30):
        try:
            response = requests.get("http://localhost:8000/health", timeout=1)
            if response.status_code == 200:
                print_colored("✅ 后端服务就绪", 92)
                break
        except:
            pass
        time.sleep(1)
    else:
        print_colored("⚠️ 后端服务启动超时", 93)
    
    # 等待前端
    for i in range(30):
        try:
            response = requests.get("http://localhost:3000", timeout=1)
            if response.status_code in [200, 404]:
                print_colored("✅ 前端服务就绪", 92)
                break
        except:
            pass
        time.sleep(1)
    else:
        print_colored("⚠️ 前端服务启动超时", 93)


def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_requirements():
        print_colored("❌ 环境检查失败，请解决上述问题后重试", 91)
        sys.exit(1)
    
    # 安装依赖
    install_backend_deps()
    if not install_frontend_deps():
        sys.exit(1)
    
    processes = []
    
    try:
        # 启动后端
        backend_process = start_backend()
        if backend_process:
            processes.append(backend_process)
        
        # 启动前端
        frontend_process = start_frontend()
        if frontend_process:
            processes.append(frontend_process)
        
        if not processes:
            print_colored("❌ 没有成功启动任何服务", 91)
            sys.exit(1)
        
        # 等待服务启动
        wait_for_services()
        
        # 显示成功信息
        print_colored("\n" + "="*60, 95)
        print_colored("🎉 服务启动完成！", 92)
        print_colored("="*60, 95)
        print_colored("📊 访问地址:", 96)
        print_colored("   前端界面: http://localhost:3000", 94)
        print_colored("   后端API: http://localhost:8000", 94)
        print_colored("   API文档: http://localhost:8000/docs", 94)
        print_colored("\n💡 现在可以运行实验脚本:", 96)
        print_colored("   python examples/simple_test.py", 94)
        print_colored("\n⚠️ 按 Ctrl+C 停止服务", 93)
        print_colored("="*60, 95)
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
                # 检查进程是否还在运行
                for process in processes[:]:
                    if process.poll() is not None:
                        processes.remove(process)
                
                if not processes:
                    print_colored("⚠️ 所有服务已停止", 93)
                    break
        except KeyboardInterrupt:
            print_colored("\n🛑 正在停止服务...", 93)
            for process in processes:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    process.kill()
            print_colored("✅ 服务已停止", 92)
    
    except Exception as e:
        print_colored(f"❌ 启动过程中发生错误: {e}", 91)
        for process in processes:
            try:
                process.terminate()
            except:
                pass
        sys.exit(1)


if __name__ == "__main__":
    main()
