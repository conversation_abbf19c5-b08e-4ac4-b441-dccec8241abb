"""
ExpManager客户端测试
"""

import pytest
import requests_mock
from unittest.mock import patch, MagicMock

from expmanager.client import Experiment<PERSON>anager, start_experiment
from expmanager.config import Config
from expmanager.exceptions import ExpManagerError, ServerNotAvailableError


class TestExperimentManager:
    """实验管理器测试类"""
    
    def test_init_default(self):
        """测试默认初始化"""
        manager = ExperimentManager()
        assert manager.api_base_url == "http://localhost:8000"
        assert manager.session.timeout == 30
    
    def test_init_with_config(self):
        """测试使用配置初始化"""
        config = Config()
        config.api_url = "http://test:9000"
        config.request_timeout = 60
        
        manager = ExperimentManager(config=config)
        assert manager.api_base_url == "http://test:9000"
        assert manager.session.timeout == 60
    
    @requests_mock.Mocker()
    def test_register_experiment_success(self, m):
        """测试成功注册实验"""
        # Mock健康检查
        m.get("http://localhost:8000/health", json={"status": "ok"})
        
        # Mock实验注册
        m.post("http://localhost:8000/api/experiments", json={
            "id": "test-exp-123",
            "name": "测试实验",
            "status": "created"
        })
        
        manager = ExperimentManager()
        
        with patch.object(manager, '_collect_metadata', return_value={}):
            exp_id = manager.register_experiment(
                name="测试实验",
                hypothesis="测试假设"
            )
        
        assert exp_id == "test-exp-123"
    
    @requests_mock.Mocker()
    def test_register_experiment_server_unavailable(self, m):
        """测试服务器不可用时的处理"""
        # Mock健康检查失败
        m.get("http://localhost:8000/health", status_code=500)
        
        manager = ExperimentManager()
        
        with pytest.raises(ServerNotAvailableError):
            manager.register_experiment("测试实验")
    
    @requests_mock.Mocker()
    def test_register_experiment_fallback_mode(self, m):
        """测试降级模式"""
        # Mock健康检查失败
        m.get("http://localhost:8000/health", status_code=500)
        
        config = Config()
        config.fallback_mode = True
        config.verbose = False
        
        manager = ExperimentManager(config=config)
        
        exp_id = manager.register_experiment("测试实验")
        
        # 应该返回临时ID
        assert exp_id.startswith("temp_")
    
    def test_collect_metadata(self):
        """测试元数据收集"""
        manager = ExperimentManager()
        
        with patch('subprocess.check_output') as mock_git:
            mock_git.return_value = b'abc123\n'
            
            metadata = manager._collect_metadata()
            
            assert metadata["git_hash"] == "abc123"
            assert "environment" in metadata
            assert "python_version" in metadata["environment"]
    
    def test_collect_metadata_no_git(self):
        """测试无Git环境的元数据收集"""
        manager = ExperimentManager()
        
        with patch('subprocess.check_output', side_effect=Exception("No git")):
            metadata = manager._collect_metadata()
            
            assert metadata["git_hash"] is None
            assert "environment" in metadata
    
    @requests_mock.Mocker()
    def test_update_experiment_status(self, m):
        """测试更新实验状态"""
        m.patch("http://localhost:8000/api/experiments/test-123", json={"status": "completed"})
        
        manager = ExperimentManager()
        
        # 应该不抛出异常
        manager._update_experiment_status("test-123", "completed")
    
    @patch('atexit.register')
    def test_setup_exit_hook(self, mock_atexit):
        """测试退出钩子设置"""
        manager = ExperimentManager()
        manager.setup_exit_hook("test-123")
        
        # 验证atexit.register被调用
        mock_atexit.assert_called_once()


class TestStartExperiment:
    """start_experiment函数测试类"""
    
    @patch('expmanager.client.ExperimentManager')
    def test_start_experiment(self, mock_manager_class):
        """测试start_experiment便捷函数"""
        mock_manager = MagicMock()
        mock_manager.register_experiment.return_value = "test-exp-456"
        mock_manager_class.return_value = mock_manager
        
        exp_id = start_experiment(
            name="便捷测试",
            hypothesis="测试假设",
            tags=["test"]
        )
        
        assert exp_id == "test-exp-456"
        mock_manager.register_experiment.assert_called_once_with(
            "便捷测试", "测试假设", None, ["test"]
        )
        mock_manager.setup_exit_hook.assert_called_once_with("test-exp-456")


class TestIntegration:
    """集成测试类"""
    
    @requests_mock.Mocker()
    @patch('webbrowser.open')
    @patch('atexit.register')
    def test_full_workflow(self, mock_atexit, mock_browser, m):
        """测试完整工作流"""
        # Mock所有API调用
        m.get("http://localhost:8000/health", json={"status": "ok"})
        m.post("http://localhost:8000/api/experiments", json={
            "id": "integration-test-789",
            "name": "集成测试",
            "status": "created"
        })
        m.patch("http://localhost:8000/api/experiments/integration-test-789", 
                json={"status": "completed"})
        
        # 执行完整流程
        with patch('subprocess.check_output', return_value=b'git-hash-123\n'):
            exp_id = start_experiment(
                name="集成测试",
                hypothesis="完整流程测试",
                description="测试从注册到完成的完整流程",
                tags=["integration", "test"]
            )
        
        assert exp_id == "integration-test-789"
        
        # 验证退出钩子被注册
        mock_atexit.assert_called_once()
        
        # 模拟脚本结束，触发退出钩子
        exit_hook = mock_atexit.call_args[0][0]
        exit_hook()
        
        # 验证浏览器被打开
        mock_browser.assert_called_once()
        expected_url = "http://localhost:3000/experiments/integration-test-789/review"
        mock_browser.assert_called_with(expected_url)
