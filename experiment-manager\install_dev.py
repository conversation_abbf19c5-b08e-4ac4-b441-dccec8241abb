#!/usr/bin/env python3
"""
ExpManager开发环境安装脚本
自动安装ExpManager库到本地Python环境
"""

import os
import sys
import subprocess
from pathlib import Path


def run_command(cmd, cwd=None):
    """运行命令并显示输出"""
    print(f"🔧 执行: {cmd}")
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            cwd=cwd,
            check=True,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("ExpManager需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_pip():
    """检查pip是否可用"""
    print("📦 检查pip...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"✅ {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip不可用")
        return False


def install_dependencies():
    """安装基础依赖"""
    print("📚 安装基础依赖...")
    
    basic_deps = [
        "requests>=2.25.0",
        "fastapi>=0.68.0", 
        "uvicorn>=0.15.0"
    ]
    
    for dep in basic_deps:
        if not run_command(f"{sys.executable} -m pip install {dep}"):
            print(f"⚠️ 依赖 {dep} 安装失败，继续...")
    
    return True


def install_expmanager():
    """安装ExpManager包"""
    print("🧪 安装ExpManager包...")
    
    # 检查当前目录
    current_dir = Path.cwd()
    setup_py = current_dir / "setup.py"
    pyproject_toml = current_dir / "pyproject.toml"
    
    if not (setup_py.exists() or pyproject_toml.exists()):
        print("❌ 未找到setup.py或pyproject.toml文件")
        print("请确保在experiment-manager目录下运行此脚本")
        return False
    
    # 开发模式安装
    if run_command(f"{sys.executable} -m pip install -e ."):
        print("✅ ExpManager安装成功（开发模式）")
        return True
    else:
        print("❌ ExpManager安装失败")
        return False


def test_installation():
    """测试安装是否成功"""
    print("🧪 测试安装...")
    
    try:
        # 测试导入
        import expmanager
        print(f"✅ ExpManager导入成功，版本: {expmanager.__version__}")
        
        # 测试基本功能
        from expmanager import start_experiment, configure
        print("✅ 核心功能导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入测试失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False


def run_simple_test():
    """运行简单测试"""
    print("🚀 运行功能测试...")
    
    test_file = Path("examples/simple_test.py")
    if test_file.exists():
        if run_command(f"{sys.executable} {test_file}"):
            print("✅ 功能测试通过")
            return True
        else:
            print("⚠️ 功能测试失败，但安装可能仍然成功")
            return True
    else:
        print("⚠️ 测试文件不存在，跳过功能测试")
        return True


def main():
    """主安装流程"""
    print("=" * 60)
    print("🚀 ExpManager开发环境安装")
    print("=" * 60)
    
    # 检查环境
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    try:
        # 安装依赖
        install_dependencies()
        
        # 安装ExpManager
        if not install_expmanager():
            print("❌ 安装失败")
            sys.exit(1)
        
        # 测试安装
        if not test_installation():
            print("❌ 安装验证失败")
            sys.exit(1)
        
        # 运行测试
        run_simple_test()
        
        print("\n" + "=" * 60)
        print("🎉 安装完成！")
        print("=" * 60)
        print("📚 现在你可以使用ExpManager了:")
        print()
        print("from expmanager import start_experiment")
        print()
        print("exp_id = start_experiment(")
        print('    name="我的实验",')
        print('    hypothesis="我的假设"')
        print(")")
        print()
        print("💡 运行示例:")
        print("   python examples/simple_test.py")
        print("   python examples/library_usage_example.py")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
