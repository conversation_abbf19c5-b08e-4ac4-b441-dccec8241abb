#!/usr/bin/env python3
"""
快速修复实验状态脚本
将指定实验的状态更新为已完成，以便能够访问复盘页面
"""

import requests
import sys
from datetime import datetime


def fix_experiment_status(experiment_id):
    """修复实验状态"""
    print(f"🔧 修复实验 {experiment_id} 的状态...")
    
    try:
        # 首先检查实验是否存在
        response = requests.get(f"http://localhost:8000/api/experiments/{experiment_id}")
        if response.status_code != 200:
            print(f"❌ 实验不存在或无法访问: {response.status_code}")
            return False
        
        experiment = response.json()
        current_status = experiment.get('status', 'unknown')
        print(f"📊 当前状态: {current_status}")
        
        if current_status == 'completed':
            print("✅ 实验已经是完成状态，无需修复")
            return True
        
        # 更新状态为已完成
        update_data = {
            "status": "completed",
            "completed_at": datetime.now().isoformat()
        }
        
        response = requests.patch(
            f"http://localhost:8000/api/experiments/{experiment_id}",
            json=update_data
        )
        
        if response.status_code == 200:
            print("✅ 实验状态已更新为已完成")
            print(f"🔗 复盘页面: http://localhost:3000/experiments/{experiment_id}/review")
            return True
        else:
            print(f"❌ 更新失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        print("💡 请确保后端服务正在运行 (http://localhost:8000)")
        return False


def main():
    """主函数"""
    print("🚀 实验状态快速修复工具")
    print("=" * 40)
    
    # 获取实验ID
    if len(sys.argv) > 1:
        experiment_id = sys.argv[1]
    else:
        experiment_id = "V7oZS6VyD4dADF26DvncV4"  # 使用用户提到的实验ID
        print(f"💡 使用默认实验ID: {experiment_id}")
    
    # 修复状态
    if fix_experiment_status(experiment_id):
        print("\n🎉 修复完成！现在可以正常访问复盘页面了")
    else:
        print("\n❌ 修复失败，请检查后端服务状态")


if __name__ == "__main__":
    main()
