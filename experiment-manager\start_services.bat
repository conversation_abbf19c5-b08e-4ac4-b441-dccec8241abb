@echo off
echo ============================================================
echo 🚀 ExpManager 服务启动脚本 (Windows)
echo ============================================================

:: 检查当前目录
if not exist "backend" (
    echo ❌ 后端目录不存在，请在experiment-manager目录下运行此脚本
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ 前端目录不存在，请在experiment-manager目录下运行此脚本
    pause
    exit /b 1
)

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装，请从 https://nodejs.org/ 下载安装
    pause
    exit /b 1
)

echo ✅ 环境检查通过

:: 安装前端依赖
echo 📦 检查前端依赖...
cd frontend
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
)
cd ..

:: 启动后端服务
echo 🚀 启动后端服务...
start "ExpManager Backend" cmd /k "cd backend && python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload"

:: 等待2秒
timeout /t 2 /nobreak >nul

:: 启动前端服务
echo 🎨 启动前端服务...
start "ExpManager Frontend" cmd /k "cd frontend && npm run dev -- --port 3000"

:: 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 5 /nobreak >nul

echo ============================================================
echo 🎉 服务启动完成！
echo ============================================================
echo 📊 访问地址:
echo    前端界面: http://localhost:3000
echo    后端API: http://localhost:8000
echo    API文档: http://localhost:8000/docs
echo.
echo 💡 现在可以运行实验脚本:
echo    python examples/simple_test.py
echo.
echo 💡 复盘页面现在应该可以正常显示了！
echo ============================================================

:: 打开浏览器
start http://localhost:3000

pause
