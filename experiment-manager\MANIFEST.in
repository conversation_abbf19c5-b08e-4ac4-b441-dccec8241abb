# 包含额外文件到分发包中

include README.md
include LICENSE
include requirements.txt
include MANIFEST.in

# 包含配置文件
recursive-include expmanager/templates *.json
recursive-include expmanager/static *

# 包含文档
recursive-include docs *.md *.rst

# 排除不需要的文件
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .DS_Store
global-exclude *.so
global-exclude *.egg-info
