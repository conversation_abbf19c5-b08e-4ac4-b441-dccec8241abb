"""
配置管理模块
基于奥卡姆剃刀原则的简洁配置系统
"""

import os
import json
from typing import Optional, Dict, Any
from pathlib import Path


class Config:
    """实验管理器配置类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径，默认查找标准位置
        """
        # 默认配置
        self.api_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.request_timeout = 30
        self.auto_open_browser = True
        self.verbose = True
        self.fallback_mode = True
        self.server_auto_start = True
        self.server_startup_timeout = 60
        
        # 加载配置文件
        self._load_config(config_file)
        
        # 环境变量覆盖
        self._load_from_env()
    
    def _load_config(self, config_file: str = None):
        """从配置文件加载设置"""
        if config_file is None:
            # 查找标准配置文件位置
            possible_paths = [
                "expmanager.json",
                "~/.expmanager/config.json",
                "~/.config/expmanager/config.json"
            ]
            
            for path in possible_paths:
                expanded_path = Path(path).expanduser()
                if expanded_path.exists():
                    config_file = str(expanded_path)
                    break
        
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self._update_from_dict(config_data)
            except Exception as e:
                if self.verbose:
                    print(f"⚠️ 配置文件加载失败: {e}")
    
    def _load_from_env(self):
        """从环境变量加载设置"""
        env_mappings = {
            "EXPMANAGER_API_URL": "api_url",
            "EXPMANAGER_FRONTEND_URL": "frontend_url", 
            "EXPMANAGER_TIMEOUT": "request_timeout",
            "EXPMANAGER_AUTO_BROWSER": "auto_open_browser",
            "EXPMANAGER_VERBOSE": "verbose",
            "EXPMANAGER_FALLBACK": "fallback_mode",
            "EXPMANAGER_AUTO_START": "server_auto_start"
        }
        
        for env_key, attr_name in env_mappings.items():
            env_value = os.environ.get(env_key)
            if env_value is not None:
                # 类型转换
                if attr_name in ["request_timeout", "server_startup_timeout"]:
                    try:
                        setattr(self, attr_name, int(env_value))
                    except ValueError:
                        pass
                elif attr_name in ["auto_open_browser", "verbose", "fallback_mode", "server_auto_start"]:
                    setattr(self, attr_name, env_value.lower() in ["true", "1", "yes", "on"])
                else:
                    setattr(self, attr_name, env_value)
    
    def _update_from_dict(self, config_dict: Dict[str, Any]):
        """从字典更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def save_config(self, config_file: str = None):
        """保存配置到文件"""
        if config_file is None:
            config_dir = Path.home() / ".expmanager"
            config_dir.mkdir(exist_ok=True)
            config_file = config_dir / "config.json"
        
        config_data = {
            "api_url": self.api_url,
            "frontend_url": self.frontend_url,
            "request_timeout": self.request_timeout,
            "auto_open_browser": self.auto_open_browser,
            "verbose": self.verbose,
            "fallback_mode": self.fallback_mode,
            "server_auto_start": self.server_auto_start,
            "server_startup_timeout": self.server_startup_timeout
        }
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            if self.verbose:
                print(f"✅ 配置已保存到: {config_file}")
        except Exception as e:
            print(f"❌ 配置保存失败: {e}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "api_url": self.api_url,
            "frontend_url": self.frontend_url,
            "request_timeout": self.request_timeout,
            "auto_open_browser": self.auto_open_browser,
            "verbose": self.verbose,
            "fallback_mode": self.fallback_mode,
            "server_auto_start": self.server_auto_start,
            "server_startup_timeout": self.server_startup_timeout
        }
    
    def __repr__(self):
        return f"Config({self.to_dict()})"


# 全局配置实例
_global_config = Config()


def configure(**kwargs) -> Config:
    """
    配置全局设置
    
    Args:
        **kwargs: 配置参数
        
    Returns:
        更新后的配置对象
        
    Example:
        >>> configure(
        ...     api_url="http://localhost:8080",
        ...     verbose=False,
        ...     auto_open_browser=False
        ... )
    """
    global _global_config
    
    for key, value in kwargs.items():
        if hasattr(_global_config, key):
            setattr(_global_config, key, value)
        else:
            print(f"⚠️ 未知配置项: {key}")
    
    return _global_config


def get_config() -> Config:
    """获取全局配置"""
    return _global_config


def reset_config():
    """重置为默认配置"""
    global _global_config
    _global_config = Config()
    return _global_config
