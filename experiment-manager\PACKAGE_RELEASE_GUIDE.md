# 📦 ExpManager Python包发布指南

> 🚀 **将实验管理系统打包成标准Python库供全球开发者使用**

## 🎯 **包设计哲学**

基于**矛盾驱动决策**的包设计：

**🔸核心矛盾**: 个人工具的便利性 vs 通用化分发的复杂性  
**💡载体转化**: 通过标准化Python包，将个人工具转化为生态平台

## 📦 **包结构概览**

```
expmanager/
├── expmanager/                 # 主包目录
│   ├── __init__.py            # 包初始化和API导出
│   ├── client.py              # 实验管理客户端
│   ├── server.py              # 服务器自动管理
│   ├── config.py              # 配置管理系统
│   ├── exceptions.py          # 异常定义
│   └── cli.py                 # 命令行工具
├── examples/                   # 使用示例
│   ├── simple_experiment.py   # 简单示例
│   └── library_usage_example.py # 完整示例
├── tests/                      # 测试文件
│   ├── __init__.py
│   └── test_client.py
├── scripts/                    # 构建脚本
│   └── build_and_publish.py
├── setup.py                    # 安装配置
├── pyproject.toml             # 现代Python包配置
├── requirements.txt           # 依赖列表
├── MANIFEST.in               # 包含文件配置
├── README.md                 # 包说明文档
├── LIBRARY_USAGE_GUIDE.md    # 详细使用指南
└── LICENSE                   # 开源许可证
```

## 🚀 **发布流程**

### 1. **环境准备**

```bash
# 安装构建工具
pip install --upgrade build twine

# 安装开发依赖
pip install -e .[dev]
```

### 2. **代码质量检查**

```bash
# 代码格式化
python -m black expmanager/

# 代码风格检查
python -m flake8 expmanager/ --max-line-length=88

# 类型检查
python -m mypy expmanager/

# 运行测试
python -m pytest tests/ -v --cov=expmanager
```

### 3. **使用构建脚本**

```bash
# 完整构建流程
python scripts/build_and_publish.py --all

# 单独步骤
python scripts/build_and_publish.py --clean    # 清理
python scripts/build_and_publish.py --test     # 测试
python scripts/build_and_publish.py --build    # 构建
```

### 4. **手动构建**

```bash
# 清理旧文件
rm -rf build/ dist/ *.egg-info/

# 构建包
python -m build

# 检查包
python -m twine check dist/*
```

### 5. **发布到PyPI**

```bash
# 发布到测试PyPI
python -m twine upload --repository testpypi dist/*

# 测试安装
pip install --index-url https://test.pypi.org/simple/ expmanager

# 发布到正式PyPI
python -m twine upload dist/*
```

## 🎯 **核心API设计**

### **最简API（奥卡姆剃刀原则）**

```python
from expmanager import start_experiment

# 一行代码启动实验管理
exp_id = start_experiment(
    name="实验名称",
    hypothesis="实验假设"
)
```

### **完整API（高级用户）**

```python
from expmanager import ExperimentManager, configure

# 全局配置
configure(
    api_url="http://localhost:8000",
    auto_open_browser=True,
    verbose=True
)

# 创建管理器
manager = ExperimentManager()
exp_id = manager.register_experiment(
    name="高级实验",
    hypothesis="详细假设",
    description="实验描述",
    tags=["tag1", "tag2"]
)
manager.setup_exit_hook(exp_id)
```

### **命令行工具**

```bash
# 服务器管理
expmanager server start
expmanager server status

# 配置管理
expmanager config --api-url http://localhost:8080
expmanager config --show

# 测试功能
expmanager test --name "测试实验"
```

## 🔧 **配置系统**

### **多层配置优先级**

1. **代码配置** (最高优先级)
2. **环境变量**
3. **配置文件**
4. **默认值** (最低优先级)

### **配置文件位置**

```
~/.expmanager/config.json
~/.config/expmanager/config.json
./expmanager.json
```

### **环境变量**

```bash
export EXPMANAGER_API_URL="http://localhost:8000"
export EXPMANAGER_AUTO_BROWSER="true"
export EXPMANAGER_VERBOSE="true"
export EXPMANAGER_FALLBACK="true"
```

## 🛡️ **错误处理策略**

### **降级模式**

当服务器不可用时，自动进入降级模式：
- 生成临时实验ID
- 跳过网络调用
- 保持脚本正常运行
- 提供友好提示

### **异常层次**

```python
ExpManagerError                    # 基础异常
├── ServerNotAvailableError       # 服务器不可用
├── ExperimentNotFoundError       # 实验未找到
├── ConfigurationError            # 配置错误
├── ValidationError               # 数据验证错误
├── NetworkError                  # 网络错误
└── ServerStartupError            # 服务器启动错误
```

## 📊 **使用场景覆盖**

### **机器学习研究**
```python
exp_id = start_experiment(
    name="CNN架构对比",
    hypothesis="ResNet比VGG准确率高5%",
    tags=["deep-learning", "cnn", "comparison"]
)
```

### **数据科学分析**
```python
exp_id = start_experiment(
    name="特征工程实验",
    hypothesis="PCA降维提升分类性能",
    tags=["feature-engineering", "pca"]
)
```

### **A/B测试**
```python
exp_id = start_experiment(
    name="推荐算法A/B测试",
    hypothesis="新算法CTR提升10%",
    tags=["ab-test", "recommendation"]
)
```

## 🎨 **用户体验设计**

### **零配置启动**
- 自动检测和启动服务器
- 智能降级处理
- 友好的错误提示

### **渐进式复杂度**
- 新手：一行代码开始
- 进阶：配置和定制
- 专家：完整API控制

### **跨平台兼容**
- Windows/Linux/Mac支持
- Python 3.8+兼容
- 依赖最小化

## 📈 **版本管理策略**

### **语义化版本**
- `1.0.0`: 首个稳定版本
- `1.1.0`: 新功能添加
- `1.0.1`: Bug修复

### **向后兼容**
- API稳定性保证
- 废弃功能渐进移除
- 迁移指南提供

## 🌍 **生态集成**

### **与现有工具集成**
- Jupyter Notebook支持
- MLflow兼容
- Git版本控制集成
- Docker容器化支持

### **扩展性设计**
- 插件系统预留
- 自定义后端支持
- 第三方工具适配

## 📞 **社区支持**

### **文档体系**
- README.md: 快速开始
- LIBRARY_USAGE_GUIDE.md: 详细指南
- API文档: 完整参考
- 示例代码: 实际应用

### **问题反馈**
- GitHub Issues: 问题追踪
- 邮件支持: 直接联系
- 社区讨论: 经验分享

## 🎉 **发布检查清单**

### **发布前检查**
- [ ] 所有测试通过
- [ ] 代码质量检查通过
- [ ] 文档更新完整
- [ ] 版本号正确
- [ ] 依赖关系明确
- [ ] 示例代码可运行
- [ ] 许可证文件存在

### **发布后验证**
- [ ] PyPI页面正常
- [ ] 安装测试成功
- [ ] 基本功能验证
- [ ] 文档链接有效
- [ ] 社区通知发送

---

<div align="center">

**🚀 ExpManager - 让实验管理触手可及**

基于矛盾驱动决策和奥卡姆剃刀原则的科学实验管理工具

[GitHub](https://github.com/deepractice/expmanager) • [PyPI](https://pypi.org/project/expmanager/) • [文档](https://github.com/deepractice/expmanager/wiki)

</div>
