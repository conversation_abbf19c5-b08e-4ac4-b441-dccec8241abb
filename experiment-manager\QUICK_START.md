# 🚀 ExpManager快速启动指南

> 解决 `ModuleNotFoundError: No module named 'expmanager'` 问题

## ❌ 问题现象

```bash
from expmanager import start_experiment
ModuleNotFoundError: No module named 'expmanager'
```

## ✅ 解决方案

### 🎯 **方案1：一键安装（推荐）**

```bash
# 进入项目目录
cd experiment-manager

# 运行自动安装脚本
python install_dev.py
```

### 🛠️ **方案2：手动安装**

```bash
# 进入项目目录
cd experiment-manager

# 开发模式安装
pip install -e .

# 或安装完整版本
pip install -e .[full]
```

### 🧪 **方案3：直接测试（无需安装）**

```bash
# 运行简单测试
python examples/simple_test.py
```

## 🔍 **验证安装**

安装完成后，运行以下代码验证：

```python
# 测试导入
from expmanager import start_experiment, configure
print("✅ ExpManager安装成功！")

# 测试基本功能
exp_id = start_experiment(
    name="安装验证测试",
    hypothesis="ExpManager已正确安装"
)
print(f"实验ID: {exp_id}")
```

## 🎉 **快速使用**

### 最简使用
```python
from expmanager import start_experiment

exp_id = start_experiment(
    name="我的第一个实验",
    hypothesis="这个假设将被验证"
)

# 你的实验代码...
print("实验进行中...")

# 脚本结束时自动打开复盘页面
```

### 完整配置
```python
from expmanager import start_experiment, configure

# 配置ExpManager
configure(
    verbose=True,                # 详细输出
    auto_open_browser=True,      # 自动打开浏览器
    fallback_mode=True          # 降级模式（服务器不可用时仍可运行）
)

# 启动实验
exp_id = start_experiment(
    name="深度学习优化实验",
    hypothesis="Adam优化器将比SGD收敛速度快20%",
    description="对比不同优化器的性能",
    tags=["deep-learning", "optimization"]
)

# 你的实验代码...
```

## 🔧 **故障排除**

### 问题1：Python版本过低
```bash
# 检查Python版本
python --version

# ExpManager需要Python 3.8+
```

### 问题2：pip不可用
```bash
# 检查pip
python -m pip --version

# 如果pip不可用，安装pip
python -m ensurepip --upgrade
```

### 问题3：权限问题
```bash
# 使用用户安装
pip install -e . --user

# 或使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
pip install -e .
```

### 问题4：依赖冲突
```bash
# 创建新的虚拟环境
python -m venv expmanager_env
source expmanager_env/bin/activate
pip install -e .
```

## 📊 **运行示例**

### 简单测试
```bash
python examples/simple_test.py
```

### 完整示例（需要scikit-learn）
```bash
pip install scikit-learn numpy
python examples/library_usage_example.py
```

## 🌐 **服务器管理**

### 启动服务器
```bash
# 使用命令行工具
expmanager server start

# 或在Python中
from expmanager.server import ensure_server_running
ensure_server_running()
```

### 检查服务器状态
```bash
expmanager server status
```

## 📋 **配置选项**

### 环境变量
```bash
export EXPMANAGER_API_URL="http://localhost:8000"
export EXPMANAGER_AUTO_BROWSER="true"
export EXPMANAGER_VERBOSE="true"
export EXPMANAGER_FALLBACK="true"
```

### 配置文件
创建 `~/.expmanager/config.json`:
```json
{
  "api_url": "http://localhost:8000",
  "auto_open_browser": true,
  "verbose": true,
  "fallback_mode": true
}
```

## 🎯 **核心特性**

- ✅ **一行代码集成**: 最简单的实验管理
- ✅ **自动复盘**: 脚本结束自动打开Web界面
- ✅ **降级模式**: 服务器不可用时仍可运行
- ✅ **跨平台**: Windows/Linux/Mac支持
- ✅ **零配置**: 开箱即用

## 📞 **获取帮助**

如果仍有问题，请：

1. **查看详细日志**:
   ```python
   from expmanager import configure
   configure(verbose=True)
   ```

2. **检查安装**:
   ```bash
   pip list | grep expmanager
   ```

3. **重新安装**:
   ```bash
   pip uninstall expmanager
   pip install -e .
   ```

4. **联系支持**:
   - GitHub Issues: https://github.com/deepractice/expmanager/issues
   - 邮箱: <EMAIL>

---

<div align="center">

**🎉 开始您的实验管理之旅！**

ExpManager让实验从"艺术"变成"工程"

</div>
