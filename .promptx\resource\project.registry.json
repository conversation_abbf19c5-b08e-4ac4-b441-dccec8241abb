{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-31T12:07:06.775Z", "updatedAt": "2025-07-31T12:07:06.778Z", "resourceCount": 6}, "resources": [{"id": "backend-engineer", "source": "project", "protocol": "role", "name": "Backend Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/backend-engineer/backend-engineer.role.md", "metadata": {"createdAt": "2025-07-31T12:07:06.775Z", "updatedAt": "2025-07-31T12:07:06.775Z", "scannedAt": "2025-07-31T12:07:06.775Z", "path": "role/backend-engineer/backend-engineer.role.md"}}, {"id": "backend-workflow", "source": "project", "protocol": "execution", "name": "Backend Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/backend-engineer/execution/backend-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T12:07:06.776Z", "updatedAt": "2025-07-31T12:07:06.776Z", "scannedAt": "2025-07-31T12:07:06.776Z", "path": "role/backend-engineer/execution/backend-workflow.execution.md"}}, {"id": "backend-development", "source": "project", "protocol": "thought", "name": "Backend Development 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/backend-engineer/thought/backend-development.thought.md", "metadata": {"createdAt": "2025-07-31T12:07:06.776Z", "updatedAt": "2025-07-31T12:07:06.776Z", "scannedAt": "2025-07-31T12:07:06.776Z", "path": "role/backend-engineer/thought/backend-development.thought.md"}}, {"id": "frontend-workflow", "source": "project", "protocol": "execution", "name": "Frontend Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/frontend-engineer/execution/frontend-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T12:07:06.777Z", "updatedAt": "2025-07-31T12:07:06.777Z", "scannedAt": "2025-07-31T12:07:06.777Z", "path": "role/frontend-engineer/execution/frontend-workflow.execution.md"}}, {"id": "frontend-engineer", "source": "project", "protocol": "role", "name": "Frontend Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/frontend-engineer/frontend-engineer.role.md", "metadata": {"createdAt": "2025-07-31T12:07:06.777Z", "updatedAt": "2025-07-31T12:07:06.777Z", "scannedAt": "2025-07-31T12:07:06.777Z", "path": "role/frontend-engineer/frontend-engineer.role.md"}}, {"id": "frontend-development", "source": "project", "protocol": "thought", "name": "Frontend Development 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/frontend-engineer/thought/frontend-development.thought.md", "metadata": {"createdAt": "2025-07-31T12:07:06.778Z", "updatedAt": "2025-07-31T12:07:06.778Z", "scannedAt": "2025-07-31T12:07:06.778Z", "path": "role/frontend-engineer/thought/frontend-development.thought.md"}}], "stats": {"totalResources": 6, "byProtocol": {"role": 2, "execution": 2, "thought": 2}, "bySource": {"project": 6}}}