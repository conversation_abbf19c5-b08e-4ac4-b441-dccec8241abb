# 📚 ExpManager库使用指南

> 🚀 **将实验从一门"艺术"变成一门"工程"**  
> 基于"三阶段十二步契约流程"的科学实验管理工具

## 🎯 核心理念

ExpManager基于**矛盾驱动决策**和**奥卡姆剃刀原则**设计：

**🔸核心矛盾**: 研究者需要专注实验创新 vs 实验管理需要繁琐记录  
**💡载体转化**: 通过一行代码将复杂管理自动化，让研究者专注实验本身

## 🚀 快速开始

### 1. 安装

```bash
# 基础安装
pip install expmanager

# 完整安装（包含服务器）
pip install expmanager[full]
```

### 2. 最简使用

```python
from expmanager import start_experiment

# 🔥 只需一行代码启动实验管理
exp_id = start_experiment(
    name="我的第一个实验",
    hypothesis="这个假设将被验证"
)

# 你的实验代码...
print("实验进行中...")

# 脚本结束时自动打开复盘页面
```

## 📖 详细使用指南

### 🔧 配置管理

#### 全局配置
```python
from expmanager import configure

configure(
    api_url="http://localhost:8000",     # API服务器地址
    frontend_url="http://localhost:3000", # 前端界面地址
    auto_open_browser=True,              # 自动打开浏览器
    verbose=True,                        # 详细输出
    fallback_mode=True,                  # 降级模式
    server_auto_start=True               # 自动启动服务器
)
```

#### 环境变量配置
```bash
export EXPMANAGER_API_URL="http://localhost:8000"
export EXPMANAGER_AUTO_BROWSER="true"
export EXPMANAGER_VERBOSE="true"
```

#### 配置文件
创建 `~/.expmanager/config.json`:
```json
{
  "api_url": "http://localhost:8000",
  "auto_open_browser": true,
  "verbose": true,
  "fallback_mode": true
}
```

### 🧪 实验管理

#### 基础实验注册
```python
from expmanager import start_experiment

exp_id = start_experiment(
    name="深度学习优化实验",
    hypothesis="Adam优化器将比SGD收敛速度快20%",
    description="对比不同优化器在CIFAR-10上的性能",
    tags=["deep-learning", "optimization", "cifar10"]
)
```

#### 高级实验管理
```python
from expmanager import ExperimentManager, configure

# 自定义配置
configure(verbose=False, auto_open_browser=False)

# 创建管理器
manager = ExperimentManager()

# 注册实验
exp_id = manager.register_experiment(
    name="高级实验",
    hypothesis="详细的实验假设",
    description="实验的详细描述",
    tags=["advanced", "custom"]
)

# 设置退出钩子
manager.setup_exit_hook(exp_id)

# 你的实验代码...
```

### 🎯 实际应用场景

#### 机器学习模型对比
```python
from expmanager import start_experiment
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression

def model_comparison_experiment():
    exp_id = start_experiment(
        name="随机森林 vs 逻辑回归",
        hypothesis="随机森林在此数据集上准确率比逻辑回归高5%",
        tags=["model-comparison", "sklearn"]
    )
    
    # 数据准备
    X_train, X_test, y_train, y_test = prepare_data()
    
    # 训练模型
    rf = RandomForestClassifier().fit(X_train, y_train)
    lr = LogisticRegression().fit(X_train, y_train)
    
    # 评估结果
    rf_score = rf.score(X_test, y_test)
    lr_score = lr.score(X_test, y_test)
    
    print(f"随机森林准确率: {rf_score:.4f}")
    print(f"逻辑回归准确率: {lr_score:.4f}")
    
    # 脚本结束时自动打开复盘页面

model_comparison_experiment()
```

#### 超参数调优实验
```python
from expmanager import start_experiment

def hyperparameter_tuning():
    exp_id = start_experiment(
        name="学习率调优实验",
        hypothesis="学习率0.001将获得最佳性能",
        tags=["hyperparameter", "tuning", "learning-rate"]
    )
    
    learning_rates = [0.1, 0.01, 0.001, 0.0001]
    results = {}
    
    for lr in learning_rates:
        model = create_model(learning_rate=lr)
        score = train_and_evaluate(model)
        results[lr] = score
        print(f"学习率 {lr}: 准确率 {score:.4f}")
    
    best_lr = max(results, key=results.get)
    print(f"最佳学习率: {best_lr}")

hyperparameter_tuning()
```

#### A/B测试实验
```python
from expmanager import start_experiment

def ab_test_experiment():
    exp_id = start_experiment(
        name="推荐算法A/B测试",
        hypothesis="新算法点击率比基线算法高10%",
        tags=["ab-test", "recommendation", "ctr"]
    )
    
    # A组：基线算法
    baseline_ctr = run_baseline_algorithm()
    
    # B组：新算法
    new_ctr = run_new_algorithm()
    
    improvement = (new_ctr - baseline_ctr) / baseline_ctr * 100
    
    print(f"基线CTR: {baseline_ctr:.4f}")
    print(f"新算法CTR: {new_ctr:.4f}")
    print(f"提升: {improvement:.2f}%")

ab_test_experiment()
```

### 🔧 命令行工具

ExpManager提供了便捷的命令行工具：

```bash
# 查看版本
expmanager --version

# 启动服务器
expmanager server start

# 查看服务器状态
expmanager server status

# 配置管理
expmanager config --api-url http://localhost:8080
expmanager config --show

# 运行测试实验
expmanager test --name "CLI测试" --hypothesis "验证CLI功能"

# 查看包信息
expmanager info
```

### 🎨 Web界面功能

实验结束后，ExpManager会自动打开Web界面，提供：

1. **📊 实验概览**: 基本信息、状态、时间线
2. **📈 数据分析**: 自动生成的统计分析
3. **🧠 智能洞察**: AI驱动的洞察建议
4. **💬 团队协作**: 评论、讨论、知识分享
5. **📁 数据导出**: JSON、CSV、PDF格式导出

### 🔄 工作流程

ExpManager遵循"三阶段十二步契约流程"：

#### 阶段一：实验前置审批
1. **立项**: 定义核心问题
2. **设计**: 确立假设与度量
3. **代码锁定**: 版本入库
4. **环境锁定**: 依赖固化
5. **数据锁定**: 数据溯源
6. **参数配置**: 配置外部化

#### 阶段二：实验执行与监控
7. **启动**: 获取唯一身份ID
8. **追踪**: 过程指标全记录
9. **产出**: 标准化归档

#### 阶段三：实验复盘与沉淀
10. **分析**: 可视化对比
11. **结论**: 撰写实验报告
12. **归档**: 标记与闭环

### 🛠️ 高级特性

#### 降级模式
当服务器不可用时，ExpManager会自动进入降级模式：
```python
from expmanager import configure

configure(fallback_mode=True)  # 启用降级模式

# 即使服务器不可用，实验仍可正常运行
exp_id = start_experiment("降级测试", "测试降级功能")
```

#### 自定义配置
```python
from expmanager import Config, ExperimentManager

# 创建自定义配置
config = Config()
config.api_url = "http://custom-server:9000"
config.verbose = False
config.auto_open_browser = False

# 使用自定义配置
manager = ExperimentManager(config=config)
```

#### 批量实验管理
```python
from expmanager import ExperimentManager

manager = ExperimentManager()

experiments = [
    ("实验1", "假设1"),
    ("实验2", "假设2"),
    ("实验3", "假设3")
]

for name, hypothesis in experiments:
    exp_id = manager.register_experiment(name, hypothesis)
    print(f"注册实验: {name} (ID: {exp_id})")
```

## 🎉 最佳实践

### 1. 实验命名规范
```python
# 好的命名
start_experiment(
    name="ResNet50_CIFAR10_Adam_lr0.001_batch32",
    hypothesis="ResNet50在CIFAR-10上使用Adam优化器(lr=0.001)准确率>95%"
)

# 避免模糊命名
start_experiment(name="测试", hypothesis="看看效果")
```

### 2. 假设明确化
```python
# 明确的假设
hypothesis = "将batch size从32增加到64将使训练时间减少20%，但准确率下降不超过1%"

# 避免模糊假设
hypothesis = "调整参数看看效果"
```

### 3. 标签体系化
```python
# 系统化标签
tags = [
    "domain:computer-vision",
    "dataset:cifar10", 
    "model:resnet50",
    "optimizer:adam",
    "experiment-type:hyperparameter-tuning"
]
```

### 4. 结合版本控制
```python
# 确保代码已提交
import subprocess

def ensure_clean_git():
    try:
        result = subprocess.run(["git", "status", "--porcelain"], 
                              capture_output=True, text=True)
        if result.stdout.strip():
            print("⚠️ 工作区有未提交的更改")
            return False
        return True
    except:
        return True

if ensure_clean_git():
    exp_id = start_experiment("清洁实验", "基于干净代码的实验")
```

## 🔧 故障排除

### 常见问题

1. **服务器启动失败**
   ```bash
   # 手动启动服务器
   expmanager server start
   
   # 检查端口占用
   netstat -an | grep 8000
   ```

2. **包导入错误**
   ```bash
   # 重新安装
   pip uninstall expmanager
   pip install expmanager[full]
   ```

3. **配置问题**
   ```bash
   # 重置配置
   expmanager config --reset
   
   # 查看当前配置
   expmanager config --show
   ```

### 调试模式
```python
from expmanager import configure

# 启用详细输出
configure(verbose=True)

# 禁用自动浏览器（便于调试）
configure(auto_open_browser=False)
```

## 📞 获取帮助

- **GitHub**: https://github.com/deepractice/expmanager
- **文档**: https://github.com/deepractice/expmanager/wiki
- **问题反馈**: https://github.com/deepractice/expmanager/issues
- **邮箱**: <EMAIL>

---

<div align="center">

**🎉 开始您的科学实验管理之旅！**

让ExpManager将您的实验从"艺术"变成"工程"

</div>
