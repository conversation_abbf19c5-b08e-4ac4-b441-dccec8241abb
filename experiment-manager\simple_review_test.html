<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化复盘页面测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%);
        }
        .container {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 1.5rem;
            padding: 2rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 1.5rem;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .title {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(135deg, #1f2937, #3b82f6);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }
        .subtitle {
            color: #6b7280;
            font-size: 1rem;
        }
        .status {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            margin: 0.5rem;
        }
        .status.loading {
            background: rgba(59, 130, 246, 0.1);
            color: #2563eb;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        .status.success {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        .status.error {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        .status.warning {
            background: rgba(245, 158, 11, 0.1);
            color: #d97706;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }
        .tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
        }
        .tab {
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }
        .tab.active {
            color: #2563eb;
            border-bottom-color: #3b82f6;
        }
        .tab:hover {
            color: #374151;
        }
        .content {
            min-height: 300px;
            padding: 1rem 0;
        }
        .experiment-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .info-item {
            padding: 1rem;
            background: rgba(249, 250, 251, 0.5);
            border-radius: 0.75rem;
            border: 1px solid rgba(229, 231, 235, 0.5);
        }
        .info-label {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }
        .info-value {
            font-weight: 500;
            color: #1f2937;
        }
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            text-align: center;
            padding: 2rem;
            color: #dc2626;
        }
        .success-message {
            text-align: center;
            padding: 2rem;
            color: #059669;
        }
        button {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">实验复盘页面测试</h1>
            <p class="subtitle">简化版复盘界面 - 用于调试和测试</p>
            <div id="connectionStatus" class="status loading">🔄 检查连接中...</div>
        </div>
    </div>

    <div class="container">
        <h2>实验信息</h2>
        <div>
            <label>实验ID: </label>
            <input type="text" id="experimentId" value="V7oZS6VyD4dADF26DvncV4" style="padding: 8px; border: 1px solid #d1d5db; border-radius: 4px; width: 300px; margin-right: 10px;">
            <button onclick="loadExperiment()">加载实验</button>
        </div>
        <div id="experimentInfo" class="experiment-info" style="margin-top: 1rem;"></div>
    </div>

    <div class="container" id="reviewContainer" style="display: none;">
        <div class="tabs">
            <button class="tab active" onclick="switchTab('analysis')">📊 数据分析</button>
            <button class="tab" onclick="switchTab('insights')">💡 洞察生成</button>
            <button class="tab" onclick="switchTab('collaboration')">👥 协作讨论</button>
            <button class="tab" onclick="switchTab('dashboard')">📈 统计仪表板</button>
        </div>
        
        <div class="content">
            <div id="analysisContent">
                <h3>📊 数据分析</h3>
                <p>这里将显示实验的数据分析结果，包括成功率、假设准确性等指标。</p>
                <div class="info-item">
                    <div class="info-label">实验成功率</div>
                    <div class="info-value">85%</div>
                </div>
            </div>
            
            <div id="insightsContent" style="display: none;">
                <h3>💡 洞察生成</h3>
                <p>这里将显示AI生成的智能洞察和建议。</p>
                <div class="info-item">
                    <div class="info-label">主要发现</div>
                    <div class="info-value">实验假设得到验证，建议继续优化参数</div>
                </div>
            </div>
            
            <div id="collaborationContent" style="display: none;">
                <h3>👥 协作讨论</h3>
                <p>这里将显示团队评论和讨论内容。</p>
                <div class="info-item">
                    <div class="info-label">团队评论</div>
                    <div class="info-value">暂无评论</div>
                </div>
            </div>
            
            <div id="dashboardContent" style="display: none;">
                <h3>📈 统计仪表板</h3>
                <p>这里将显示复盘统计和趋势分析。</p>
                <div class="info-item">
                    <div class="info-label">复盘完成度</div>
                    <div class="info-value">60%</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentExperiment = null;

        // 页面加载时检查连接
        window.onload = function() {
            checkConnection();
            loadExperiment();
        };

        // 检查后端连接
        async function checkConnection() {
            const statusDiv = document.getElementById('connectionStatus');
            
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    statusDiv.textContent = '✅ 后端连接正常';
                    statusDiv.className = 'status success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusDiv.textContent = `❌ 后端连接失败: ${error.message}`;
                statusDiv.className = 'status error';
            }
        }

        // 加载实验信息
        async function loadExperiment() {
            const experimentId = document.getElementById('experimentId').value;
            const infoDiv = document.getElementById('experimentInfo');
            const reviewContainer = document.getElementById('reviewContainer');
            
            if (!experimentId) {
                infoDiv.innerHTML = '<div class="error-message">请输入实验ID</div>';
                return;
            }

            infoDiv.innerHTML = '<div class="loading-spinner"></div> 加载中...';
            reviewContainer.style.display = 'none';

            try {
                const response = await fetch(`http://localhost:8000/api/experiments/${experimentId}`);
                
                if (response.ok) {
                    const experiment = await response.json();
                    currentExperiment = experiment;
                    
                    // 显示实验信息
                    infoDiv.innerHTML = `
                        <div class="info-item">
                            <div class="info-label">实验名称</div>
                            <div class="info-value">${experiment.name || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">实验状态</div>
                            <div class="info-value">
                                <span class="status ${experiment.status === 'completed' ? 'success' : 'warning'}">
                                    ${experiment.status || 'unknown'}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">创建时间</div>
                            <div class="info-value">${experiment.created_at ? new Date(experiment.created_at).toLocaleString('zh-CN') : 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">完成时间</div>
                            <div class="info-value">${experiment.completed_at ? new Date(experiment.completed_at).toLocaleString('zh-CN') : '未完成'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">实验描述</div>
                            <div class="info-value">${experiment.description || experiment.hypothesis || 'N/A'}</div>
                        </div>
                    `;

                    // 如果实验已完成，显示复盘界面
                    if (experiment.status === 'completed') {
                        reviewContainer.style.display = 'block';
                    } else {
                        infoDiv.innerHTML += `
                            <div class="info-item">
                                <div class="info-label">提示</div>
                                <div class="info-value">
                                    实验尚未完成，无法进行复盘分析
                                    <button onclick="markAsCompleted()">标记为已完成</button>
                                </div>
                            </div>
                        `;
                    }
                } else if (response.status === 404) {
                    infoDiv.innerHTML = '<div class="error-message">❌ 实验不存在</div>';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                infoDiv.innerHTML = `<div class="error-message">❌ 加载失败: ${error.message}</div>`;
            }
        }

        // 标记实验为已完成
        async function markAsCompleted() {
            const experimentId = document.getElementById('experimentId').value;
            
            try {
                const response = await fetch(`http://localhost:8000/api/experiments/${experimentId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        status: 'completed',
                        completed_at: new Date().toISOString()
                    })
                });

                if (response.ok) {
                    alert('✅ 实验状态已更新为已完成');
                    loadExperiment(); // 重新加载
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                alert(`❌ 更新失败: ${error.message}`);
            }
        }

        // 切换标签
        function switchTab(tabName) {
            // 隐藏所有内容
            document.getElementById('analysisContent').style.display = 'none';
            document.getElementById('insightsContent').style.display = 'none';
            document.getElementById('collaborationContent').style.display = 'none';
            document.getElementById('dashboardContent').style.display = 'none';

            // 移除所有活动状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(tabName + 'Content').style.display = 'block';
            
            // 添加活动状态
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
