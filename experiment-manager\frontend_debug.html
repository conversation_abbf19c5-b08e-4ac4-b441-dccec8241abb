<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复盘页面调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .debug-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .debug-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-good { color: #059669; }
        .status-warning { color: #d97706; }
        .status-error { color: #dc2626; }
        .debug-item {
            padding: 10px;
            margin: 8px 0;
            border-radius: 8px;
            border-left: 4px solid #e5e7eb;
        }
        .debug-item.good { border-left-color: #10b981; background: #f0fdf4; }
        .debug-item.warning { border-left-color: #f59e0b; background: #fffbeb; }
        .debug-item.error { border-left-color: #ef4444; background: #fef2f2; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        .json-display {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>🔧 复盘页面调试工具</h1>
    
    <div class="debug-section">
        <div class="debug-title">
            🎯 实验信息检查
        </div>
        <div>
            <label>实验ID: </label>
            <input type="text" id="experimentId" value="V7oZS6VyD4dADF26DvncV4" style="padding: 8px; border: 1px solid #d1d5db; border-radius: 4px; width: 300px;">
            <button onclick="checkExperiment()">检查实验</button>
        </div>
        <div id="experimentResult"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">
            🌐 服务连接检查
        </div>
        <div id="serviceStatus">
            <div class="debug-item">
                <span id="backendStatus">🔄 检查中...</span>
            </div>
            <div class="debug-item">
                <span id="frontendStatus">🔄 检查中...</span>
            </div>
        </div>
    </div>

    <div class="debug-section">
        <div class="debug-title">
            📊 API测试
        </div>
        <button onclick="testHealthAPI()">测试健康检查API</button>
        <button onclick="testExperimentAPI()">测试实验API</button>
        <button onclick="testReviewPage()">测试复盘页面</button>
        <div id="apiResults"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">
            🔍 前端错误检查
        </div>
        <button onclick="checkConsoleErrors()">检查控制台错误</button>
        <button onclick="checkNetworkErrors()">检查网络错误</button>
        <div id="errorResults"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">
            🛠️ 快速修复
        </div>
        <button onclick="fixExperimentStatus()">修复实验状态</button>
        <button onclick="clearCache()">清除缓存</button>
        <button onclick="openReviewPage()">打开复盘页面</button>
        <div id="fixResults"></div>
    </div>

    <script>
        // 全局变量
        let currentExperimentId = 'V7oZS6VyD4dADF26DvncV4';
        
        // 页面加载时自动检查
        window.onload = function() {
            checkServices();
            checkExperiment();
        };

        // 检查服务状态
        async function checkServices() {
            // 检查后端
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    document.getElementById('backendStatus').innerHTML = '✅ 后端服务正常 (http://localhost:8000)';
                    document.getElementById('backendStatus').parentElement.className = 'debug-item good';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('backendStatus').innerHTML = `❌ 后端服务异常: ${error.message}`;
                document.getElementById('backendStatus').parentElement.className = 'debug-item error';
            }

            // 检查前端
            try {
                const response = await fetch('http://localhost:3000');
                if (response.ok || response.status === 404) {
                    document.getElementById('frontendStatus').innerHTML = '✅ 前端服务正常 (http://localhost:3000)';
                    document.getElementById('frontendStatus').parentElement.className = 'debug-item good';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('frontendStatus').innerHTML = `❌ 前端服务异常: ${error.message}`;
                document.getElementById('frontendStatus').parentElement.className = 'debug-item error';
            }
        }

        // 检查实验
        async function checkExperiment() {
            const experimentId = document.getElementById('experimentId').value || currentExperimentId;
            const resultDiv = document.getElementById('experimentResult');
            
            resultDiv.innerHTML = '<div class="loading"></div> 检查中...';
            
            try {
                const response = await fetch(`http://localhost:8000/api/experiments/${experimentId}`);
                if (response.ok) {
                    const experiment = await response.json();
                    let statusClass = 'good';
                    let statusText = '✅';
                    
                    if (experiment.status !== 'completed') {
                        statusClass = 'warning';
                        statusText = '⚠️';
                    }
                    
                    resultDiv.innerHTML = `
                        <div class="debug-item ${statusClass}">
                            ${statusText} 实验存在
                            <div style="margin-top: 8px; font-size: 14px;">
                                <strong>名称:</strong> ${experiment.name}<br>
                                <strong>状态:</strong> ${experiment.status}<br>
                                <strong>创建时间:</strong> ${experiment.created_at}<br>
                                <strong>完成时间:</strong> ${experiment.completed_at || '未完成'}
                            </div>
                        </div>
                    `;
                } else if (response.status === 404) {
                    resultDiv.innerHTML = `
                        <div class="debug-item error">
                            ❌ 实验不存在 (ID: ${experimentId})
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="debug-item error">
                        ❌ 检查失败: ${error.message}
                    </div>
                `;
            }
        }

        // 测试健康检查API
        async function testHealthAPI() {
            const resultDiv = document.getElementById('apiResults');
            try {
                const response = await fetch('http://localhost:8000/health');
                const data = await response.json();
                resultDiv.innerHTML = `
                    <div class="debug-item good">
                        ✅ 健康检查API正常
                        <div class="json-display">${JSON.stringify(data, null, 2)}</div>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="debug-item error">
                        ❌ 健康检查API失败: ${error.message}
                    </div>
                `;
            }
        }

        // 测试实验API
        async function testExperimentAPI() {
            const experimentId = document.getElementById('experimentId').value || currentExperimentId;
            const resultDiv = document.getElementById('apiResults');
            
            try {
                const response = await fetch(`http://localhost:8000/api/experiments/${experimentId}`);
                const data = await response.json();
                resultDiv.innerHTML = `
                    <div class="debug-item good">
                        ✅ 实验API正常
                        <div class="json-display">${JSON.stringify(data, null, 2)}</div>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="debug-item error">
                        ❌ 实验API失败: ${error.message}
                    </div>
                `;
            }
        }

        // 测试复盘页面
        async function testReviewPage() {
            const experimentId = document.getElementById('experimentId').value || currentExperimentId;
            const reviewUrl = `http://localhost:3000/experiments/${experimentId}/review`;
            
            try {
                const response = await fetch(reviewUrl);
                const resultDiv = document.getElementById('apiResults');
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="debug-item good">
                            ✅ 复盘页面可访问
                            <div style="margin-top: 8px;">
                                <a href="${reviewUrl}" target="_blank" style="color: #3b82f6;">打开复盘页面</a>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="debug-item warning">
                            ⚠️ 复盘页面响应异常 (${response.status})
                            <div style="margin-top: 8px;">
                                <a href="${reviewUrl}" target="_blank" style="color: #3b82f6;">尝试打开页面</a>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('apiResults').innerHTML = `
                    <div class="debug-item error">
                        ❌ 复盘页面测试失败: ${error.message}
                    </div>
                `;
            }
        }

        // 修复实验状态
        async function fixExperimentStatus() {
            const experimentId = document.getElementById('experimentId').value || currentExperimentId;
            const resultDiv = document.getElementById('fixResults');
            
            resultDiv.innerHTML = '<div class="loading"></div> 修复中...';
            
            try {
                const response = await fetch(`http://localhost:8000/api/experiments/${experimentId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        status: 'completed',
                        completed_at: new Date().toISOString()
                    })
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="debug-item good">
                            ✅ 实验状态已修复为已完成
                            <div style="margin-top: 8px;">
                                <button onclick="openReviewPage()">打开复盘页面</button>
                            </div>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="debug-item error">
                        ❌ 修复失败: ${error.message}
                    </div>
                `;
            }
        }

        // 打开复盘页面
        function openReviewPage() {
            const experimentId = document.getElementById('experimentId').value || currentExperimentId;
            const reviewUrl = `http://localhost:3000/experiments/${experimentId}/review`;
            window.open(reviewUrl, '_blank');
        }

        // 检查控制台错误
        function checkConsoleErrors() {
            const resultDiv = document.getElementById('errorResults');
            resultDiv.innerHTML = `
                <div class="debug-item">
                    📋 请打开浏览器开发者工具 (F12) 检查：
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>Console 标签中的 JavaScript 错误</li>
                        <li>Network 标签中的网络请求失败</li>
                        <li>刷新复盘页面观察错误信息</li>
                    </ul>
                </div>
            `;
        }

        // 清除缓存
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            localStorage.clear();
            sessionStorage.clear();
            
            document.getElementById('fixResults').innerHTML = `
                <div class="debug-item good">
                    ✅ 缓存已清除，请刷新页面
                </div>
            `;
        }
    </script>
</body>
</html>
