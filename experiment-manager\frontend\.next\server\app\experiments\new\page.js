/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/experiments/new/page";
exports.ids = ["app/experiments/new/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fexperiments%2Fnew%2Fpage&page=%2Fexperiments%2Fnew%2Fpage&appPaths=%2Fexperiments%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fexperiments%2Fnew%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fexperiments%2Fnew%2Fpage&page=%2Fexperiments%2Fnew%2Fpage&appPaths=%2Fexperiments%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fexperiments%2Fnew%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'experiments',\n        {\n        children: [\n        'new',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/experiments/new/page.tsx */ \"(rsc)/./src/app/experiments/new/page.tsx\")), \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/experiments/new/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/experiments/new/page\",\n        pathname: \"/experiments/new\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fexperiments%2Fnew%2Fpage&page=%2Fexperiments%2Fnew%2Fpage&appPaths=%2Fexperiments%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fexperiments%2Fnew%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmFwcC1yb3V0ZXIuanMmbW9kdWxlcz0lMkZtbnQlMkZlJTJGMDEtbWFpbiUyRmZyYW1ld29yayUyRmV4cGVyaW1lbnQtbWFuYWdlciUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGZXJyb3ItYm91bmRhcnkuanMmbW9kdWxlcz0lMkZtbnQlMkZlJTJGMDEtbWFpbiUyRmZyYW1ld29yayUyRmV4cGVyaW1lbnQtbWFuYWdlciUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZub3QtZm91bmQtYm91bmRhcnkuanMmbW9kdWxlcz0lMkZtbnQlMkZlJTJGMDEtbWFpbiUyRmZyYW1ld29yayUyRmV4cGVyaW1lbnQtbWFuYWdlciUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWdKO0FBQ2hKLDBPQUFvSjtBQUNwSix3T0FBbUo7QUFDbkosa1BBQXdKO0FBQ3hKLHNRQUFrSztBQUNsSyIsInNvdXJjZXMiOlsid2VicGFjazovL2V4cGVyaW1lbnQtbWFuYWdlci1mcm9udGVuZC8/ODkwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9tbnQvZS8wMS1tYWluL2ZyYW1ld29yay9leHBlcmltZW50LW1hbmFnZXIvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9hcHAtcm91dGVyLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbW50L2UvMDEtbWFpbi9mcmFtZXdvcmsvZXhwZXJpbWVudC1tYW5hZ2VyL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9tbnQvZS8wMS1tYWluL2ZyYW1ld29yay9leHBlcmltZW50LW1hbmFnZXIvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbW50L2UvMDEtbWFpbi9mcmFtZXdvcmsvZXhwZXJpbWVudC1tYW5hZ2VyL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbW50L2UvMDEtbWFpbi9mcmFtZXdvcmsvZXhwZXJpbWVudC1tYW5hZ2VyL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21udC9lLzAxLW1haW4vZnJhbWV3b3JrL2V4cGVyaW1lbnQtbWFuYWdlci9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3N0YXRpYy1nZW5lcmF0aW9uLXNlYXJjaHBhcmFtcy1iYWlsb3V0LXByb3ZpZGVyLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Ftest-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fstyles%2Fmodern-theme.css&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Ftest-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fstyles%2Fmodern-theme.css&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/test-layout.tsx */ \"(ssr)/./src/components/layout/test-layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGdGVzdC1sYXlvdXQudHN4Jm1vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRnNyYyUyRnN0eWxlcyUyRm1vZGVybi10aGVtZS5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLz8zZTNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21udC9lLzAxLW1haW4vZnJhbWV3b3JrL2V4cGVyaW1lbnQtbWFuYWdlci9mcm9udGVuZC9zcmMvY29tcG9uZW50cy9sYXlvdXQvdGVzdC1sYXlvdXQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Ftest-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fstyles%2Fmodern-theme.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fexperiments%2Fnew%2Fpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fexperiments%2Fnew%2Fpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/experiments/new/page.tsx */ \"(ssr)/./src/app/experiments/new/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmV4cGVyaW1lbnRzJTJGbmV3JTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLz83MGRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21udC9lLzAxLW1haW4vZnJhbWV3b3JrL2V4cGVyaW1lbnQtbWFuYWdlci9mcm9udGVuZC9zcmMvYXBwL2V4cGVyaW1lbnRzL25ldy9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fexperiments%2Fnew%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/experiments/new/page.tsx":
/*!******************************************!*\
  !*** ./src/app/experiments/new/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewExperimentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FlaskConical,Lightbulb,Save,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FlaskConical,Lightbulb,Save,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flask-conical.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FlaskConical,Lightbulb,Save,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FlaskConical,Lightbulb,Save,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,FlaskConical,Lightbulb,Save,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/**\n * 创建实验页面\n * 现代化设计，使用内联样式\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NewExperimentPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        hypothesis: \"\",\n        description: \"\",\n        expectedOutcome: \"\",\n        successMetrics: \"\"\n    });\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            // 模拟API调用\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            // 创建成功后跳转\n            router.push(\"/experiments\");\n        } catch (error) {\n            console.error(\"创建实验失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const isFormValid = formData.name && formData.hypothesis && formData.description;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: \"2rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/experiments\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: \"rgba(255, 255, 255, 0.8)\",\n                                        border: \"1px solid rgba(229, 231, 235, 0.5)\",\n                                        borderRadius: \"0.5rem\",\n                                        padding: \"0.5rem 1rem\",\n                                        cursor: \"pointer\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"0.5rem\",\n                                        fontSize: \"0.875rem\",\n                                        transition: \"all 0.3s ease\",\n                                        textDecoration: \"none\",\n                                        color: \"#374151\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"rgba(243, 244, 246, 0.8)\";\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.8)\";\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            style: {\n                                                height: \"1rem\",\n                                                width: \"1rem\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"返回实验列表\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.75rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"0.75rem\",\n                                            background: \"linear-gradient(to right, #3b82f6, #8b5cf6)\",\n                                            borderRadius: \"0.75rem\",\n                                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            style: {\n                                                height: \"2rem\",\n                                                width: \"2rem\",\n                                                color: \"white\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    fontSize: \"clamp(1.875rem, 4vw, 2.25rem)\",\n                                                    fontWeight: \"bold\",\n                                                    background: \"linear-gradient(to right, #2563eb, #8b5cf6)\",\n                                                    WebkitBackgroundClip: \"text\",\n                                                    backgroundClip: \"text\",\n                                                    WebkitTextFillColor: \"transparent\",\n                                                    color: \"transparent\",\n                                                    margin: 0\n                                                },\n                                                children: \"创建新实验\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: \"#6b7280\",\n                                                    margin: \"0.25rem 0 0 0\"\n                                                },\n                                                children: \"设计您的科研实验方案\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        style: {\n                            fontSize: \"0.875rem\",\n                            fontWeight: \"500\",\n                            color: \"#2563eb\",\n                            padding: \"0.5rem 1rem\",\n                            borderRadius: \"0.5rem\",\n                            border: \"1px solid rgba(147, 197, 253, 0.5)\",\n                            background: \"rgba(239, 246, 255, 0.8)\"\n                        },\n                        children: \"实验设计阶段\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                style: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    gap: \"1.5rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(255, 255, 255, 0.8)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"1rem\",\n                            padding: \"2rem\",\n                            border: \"1px solid rgba(229, 231, 235, 0.5)\",\n                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.5rem\",\n                                    marginBottom: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        style: {\n                                            height: \"1.25rem\",\n                                            width: \"1.25rem\",\n                                            color: \"#eab308\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            fontSize: \"1.25rem\",\n                                            fontWeight: \"600\",\n                                            margin: 0\n                                        },\n                                        children: \"基本信息\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    fontSize: \"0.875rem\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#374151\",\n                                                    marginBottom: \"0.5rem\"\n                                                },\n                                                children: [\n                                                    \"实验名称 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"#ef4444\"\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 22\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                placeholder: \"请输入实验名称，例如：深度学习模型训练实验\",\n                                                required: true,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"3rem\",\n                                                    fontSize: \"1rem\",\n                                                    border: \"1px solid #d1d5db\",\n                                                    borderRadius: \"0.5rem\",\n                                                    padding: \"0 1rem\",\n                                                    outline: \"none\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onFocus: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#3b82f6\";\n                                                    e.currentTarget.style.boxShadow = \"0 0 0 3px rgba(59, 130, 246, 0.1)\";\n                                                },\n                                                onBlur: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#d1d5db\";\n                                                    e.currentTarget.style.boxShadow = \"none\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    fontSize: \"0.875rem\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#374151\",\n                                                    marginBottom: \"0.5rem\"\n                                                },\n                                                children: [\n                                                    \"实验假设 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"#ef4444\"\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 22\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.hypothesis,\n                                                onChange: (e)=>handleInputChange(\"hypothesis\", e.target.value),\n                                                placeholder: \"请描述您的实验假设，例如：使用ResNet架构可以提高图像分类准确率至95%以上\",\n                                                required: true,\n                                                style: {\n                                                    width: \"100%\",\n                                                    minHeight: \"6rem\",\n                                                    fontSize: \"1rem\",\n                                                    border: \"1px solid #d1d5db\",\n                                                    borderRadius: \"0.5rem\",\n                                                    padding: \"0.75rem 1rem\",\n                                                    outline: \"none\",\n                                                    resize: \"vertical\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onFocus: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#3b82f6\";\n                                                    e.currentTarget.style.boxShadow = \"0 0 0 3px rgba(59, 130, 246, 0.1)\";\n                                                },\n                                                onBlur: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#d1d5db\";\n                                                    e.currentTarget.style.boxShadow = \"none\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    fontSize: \"0.875rem\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#374151\",\n                                                    marginBottom: \"0.5rem\"\n                                                },\n                                                children: [\n                                                    \"实验描述 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"#ef4444\"\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 22\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange(\"description\", e.target.value),\n                                                placeholder: \"详细描述实验的背景、目的、方法等\",\n                                                required: true,\n                                                style: {\n                                                    width: \"100%\",\n                                                    minHeight: \"8rem\",\n                                                    fontSize: \"1rem\",\n                                                    border: \"1px solid #d1d5db\",\n                                                    borderRadius: \"0.5rem\",\n                                                    padding: \"0.75rem 1rem\",\n                                                    outline: \"none\",\n                                                    resize: \"vertical\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onFocus: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#3b82f6\";\n                                                    e.currentTarget.style.boxShadow = \"0 0 0 3px rgba(59, 130, 246, 0.1)\";\n                                                },\n                                                onBlur: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#d1d5db\";\n                                                    e.currentTarget.style.boxShadow = \"none\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"rgba(255, 255, 255, 0.8)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"1rem\",\n                            padding: \"2rem\",\n                            border: \"1px solid rgba(229, 231, 235, 0.5)\",\n                            boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.5rem\",\n                                    marginBottom: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        style: {\n                                            height: \"1.25rem\",\n                                            width: \"1.25rem\",\n                                            color: \"#10b981\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            fontSize: \"1.25rem\",\n                                            fontWeight: \"600\",\n                                            margin: 0\n                                        },\n                                        children: \"预期结果\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    fontSize: \"0.875rem\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#374151\",\n                                                    marginBottom: \"0.5rem\"\n                                                },\n                                                children: \"预期结果\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.expectedOutcome,\n                                                onChange: (e)=>handleInputChange(\"expectedOutcome\", e.target.value),\n                                                placeholder: \"描述您期望的实验结果\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    minHeight: \"6rem\",\n                                                    fontSize: \"1rem\",\n                                                    border: \"1px solid #d1d5db\",\n                                                    borderRadius: \"0.5rem\",\n                                                    padding: \"0.75rem 1rem\",\n                                                    outline: \"none\",\n                                                    resize: \"vertical\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onFocus: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#3b82f6\";\n                                                    e.currentTarget.style.boxShadow = \"0 0 0 3px rgba(59, 130, 246, 0.1)\";\n                                                },\n                                                onBlur: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#d1d5db\";\n                                                    e.currentTarget.style.boxShadow = \"none\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: \"block\",\n                                                    fontSize: \"0.875rem\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#374151\",\n                                                    marginBottom: \"0.5rem\"\n                                                },\n                                                children: \"成功指标\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.successMetrics,\n                                                onChange: (e)=>handleInputChange(\"successMetrics\", e.target.value),\n                                                placeholder: \"定义如何衡量实验成功\",\n                                                style: {\n                                                    width: \"100%\",\n                                                    minHeight: \"6rem\",\n                                                    fontSize: \"1rem\",\n                                                    border: \"1px solid #d1d5db\",\n                                                    borderRadius: \"0.5rem\",\n                                                    padding: \"0.75rem 1rem\",\n                                                    outline: \"none\",\n                                                    resize: \"vertical\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onFocus: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#3b82f6\";\n                                                    e.currentTarget.style.boxShadow = \"0 0 0 3px rgba(59, 130, 246, 0.1)\";\n                                                },\n                                                onBlur: (e)=>{\n                                                    e.currentTarget.style.borderColor = \"#d1d5db\";\n                                                    e.currentTarget.style.boxShadow = \"none\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"flex-end\",\n                            gap: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/experiments\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    style: {\n                                        background: \"white\",\n                                        border: \"1px solid #d1d5db\",\n                                        borderRadius: \"0.5rem\",\n                                        padding: \"0.75rem 1.5rem\",\n                                        fontSize: \"0.875rem\",\n                                        fontWeight: \"600\",\n                                        cursor: \"pointer\",\n                                        transition: \"all 0.3s ease\",\n                                        textDecoration: \"none\",\n                                        color: \"#374151\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"#f9fafb\";\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.currentTarget.style.backgroundColor = \"white\";\n                                    },\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: !isFormValid || loading,\n                                style: {\n                                    background: isFormValid && !loading ? \"linear-gradient(to right, #2563eb, #3b82f6)\" : \"#d1d5db\",\n                                    color: isFormValid && !loading ? \"white\" : \"#9ca3af\",\n                                    border: \"none\",\n                                    borderRadius: \"0.5rem\",\n                                    padding: \"0.75rem 1.5rem\",\n                                    fontSize: \"0.875rem\",\n                                    fontWeight: \"600\",\n                                    cursor: isFormValid && !loading ? \"pointer\" : \"not-allowed\",\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.5rem\",\n                                    transition: \"all 0.3s ease\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    if (isFormValid && !loading) {\n                                        e.currentTarget.style.background = \"linear-gradient(to right, #1d4ed8, #2563eb)\";\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (isFormValid && !loading) {\n                                        e.currentTarget.style.background = \"linear-gradient(to right, #2563eb, #3b82f6)\";\n                                    }\n                                },\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"1rem\",\n                                                height: \"1rem\",\n                                                border: \"2px solid transparent\",\n                                                borderTop: \"2px solid currentColor\",\n                                                borderRadius: \"50%\",\n                                                animation: \"spin 1s linear infinite\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"创建中...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_FlaskConical_Lightbulb_Save_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            style: {\n                                                height: \"1rem\",\n                                                width: \"1rem\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"创建实验\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/experiments/new/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/test-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/test-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TestLayout: () => (/* binding */ TestLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flask-conical.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* __next_internal_client_entry_do_not_use__ TestLayout auto */ \n\n\n\nfunction TestLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            background: \"linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    zIndex: 50,\n                    background: \"rgba(255, 255, 255, 0.9)\",\n                    backdropFilter: \"blur(20px)\",\n                    borderBottom: \"1px solid rgba(229, 231, 235, 0.5)\",\n                    boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1)\",\n                    transition: \"all 0.3s ease\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: \"80rem\",\n                        margin: \"0 auto\",\n                        padding: \"0 1rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"space-between\",\n                            height: \"4rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"1rem\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"relative\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        inset: 0,\n                                                        background: \"linear-gradient(to right, #2563eb, #8b5cf6, #4f46e5)\",\n                                                        borderRadius: \"0.75rem\",\n                                                        filter: \"blur(4px)\",\n                                                        opacity: 0.3,\n                                                        transform: \"scale(1.1)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"relative\",\n                                                        height: \"2.5rem\",\n                                                        width: \"2.5rem\",\n                                                        background: \"linear-gradient(to right, #2563eb, #8b5cf6, #4f46e5)\",\n                                                        borderRadius: \"0.75rem\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\",\n                                                        transition: \"all 0.3s ease\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        style: {\n                                                            height: \"1.5rem\",\n                                                            width: \"1.5rem\",\n                                                            color: \"white\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"block\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"1.25rem\",\n                                                        fontWeight: \"bold\",\n                                                        background: \"linear-gradient(to right, #1f2937, #2563eb, #8b5cf6)\",\n                                                        WebkitBackgroundClip: \"text\",\n                                                        backgroundClip: \"text\",\n                                                        WebkitTextFillColor: \"transparent\",\n                                                        color: \"transparent\"\n                                                    },\n                                                    children: \"实验管理系统\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"0.75rem\",\n                                                        color: \"#6b7280\",\n                                                        marginTop: \"-0.25rem\"\n                                                    },\n                                                    children: \"Experiment Manager Pro\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        style: {\n                                            position: \"relative\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.625rem 1.25rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            color: \"white\",\n                                            background: \"linear-gradient(to right, #3b82f6, #06b6d4)\",\n                                            boxShadow: \"0 20px 25px -5px rgba(59, 130, 246, 0.3)\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.transform = \"scale(1.05)\";\n                                            e.currentTarget.style.boxShadow = \"0 25px 50px -12px rgba(59, 130, 246, 0.4)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.transform = \"scale(1)\";\n                                            e.currentTarget.style.boxShadow = \"0 20px 25px -5px rgba(59, 130, 246, 0.3)\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"仪表板\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/experiments\",\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.625rem 1.25rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            color: \"#374151\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.7)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#374151\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"实验中心\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/experiments/new\",\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.625rem 1.25rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            color: \"#374151\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.7)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#374151\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"创建实验\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/settings\",\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.625rem 1.25rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            color: \"#374151\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.7)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#374151\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"系统设置\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.75rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: \"relative\",\n                                            display: \"flex\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    inset: 0,\n                                                    background: \"linear-gradient(to right, rgba(59, 130, 246, 0.15), rgba(139, 92, 246, 0.15))\",\n                                                    borderRadius: \"0.75rem\",\n                                                    filter: \"blur(4px)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"relative\",\n                                                    background: \"rgba(255, 255, 255, 0.7)\",\n                                                    backdropFilter: \"blur(12px)\",\n                                                    borderRadius: \"0.75rem\",\n                                                    border: \"1px solid rgba(229, 231, 235, 0.5)\",\n                                                    boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            left: \"1rem\",\n                                                            top: \"50%\",\n                                                            transform: \"translateY(-50%)\",\n                                                            height: \"1.25rem\",\n                                                            width: \"1.25rem\",\n                                                            color: \"#6b7280\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"search\",\n                                                        placeholder: \"搜索实验、数据、报告...\",\n                                                        style: {\n                                                            paddingLeft: \"3rem\",\n                                                            paddingRight: \"1rem\",\n                                                            width: \"18rem\",\n                                                            height: \"2.5rem\",\n                                                            background: \"transparent\",\n                                                            border: \"none\",\n                                                            outline: \"none\",\n                                                            color: \"#374151\",\n                                                            fontSize: \"0.875rem\",\n                                                            fontWeight: \"500\",\n                                                            borderRadius: \"0.75rem\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            position: \"relative\",\n                                            padding: \"0.5rem\",\n                                            color: \"#6b7280\",\n                                            borderRadius: \"0.75rem\",\n                                            border: \"none\",\n                                            background: \"transparent\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.5)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#6b7280\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    top: \"0.25rem\",\n                                                    right: \"0.25rem\",\n                                                    height: \"0.5rem\",\n                                                    width: \"0.5rem\",\n                                                    backgroundColor: \"#ef4444\",\n                                                    borderRadius: \"50%\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            position: \"relative\",\n                                            padding: \"0.5rem\",\n                                            color: \"#6b7280\",\n                                            borderRadius: \"0.75rem\",\n                                            border: \"none\",\n                                            background: \"transparent\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.5)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#6b7280\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            style: {\n                                                height: \"1.25rem\",\n                                                width: \"1.25rem\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    paddingTop: \"4rem\",\n                    minHeight: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: \"80rem\",\n                        margin: \"0 auto\",\n                        padding: \"0 1rem 2rem 1rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    inset: 0,\n                                    background: \"linear-gradient(to right, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.05))\",\n                                    borderRadius: \"3rem\",\n                                    filter: \"blur(48px)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    background: \"rgba(255, 255, 255, 0.4)\",\n                                    backdropFilter: \"blur(10px)\",\n                                    borderRadius: \"1rem\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.05)\",\n                                    overflow: \"hidden\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        padding: \"1.5rem 2rem\"\n                                    },\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/test-layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"347bc58f1c86\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8xMjczIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzQ3YmM1OGYxYzg2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/modern-theme.css":
/*!*************************************!*\
  !*** ./src/styles/modern-theme.css ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d87aeb22b8de\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL21vZGVybi10aGVtZS5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leHBlcmltZW50LW1hbmFnZXItZnJvbnRlbmQvLi9zcmMvc3R5bGVzL21vZGVybi10aGVtZS5jc3M/YWEyZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQ4N2FlYjIyYjhkZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/modern-theme.css\n");

/***/ }),

/***/ "(rsc)/./src/app/experiments/new/page.tsx":
/*!******************************************!*\
  !*** ./src/app/experiments/new/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $$typeof: () => (/* binding */ $$typeof),\n/* harmony export */   __esModule: () => (/* binding */ __esModule),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\nconst proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/app/experiments/new/page.tsx`)\n\n// Accessing the __esModule property and exporting $$typeof are required here.\n// The __esModule getter forces the proxy target to create the default export\n// and the $$typeof value is for rendering logic to determine if the module\n// is a client boundary.\nconst { __esModule, $$typeof } = proxy;\nconst __default__ = proxy.default;\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/experiments/new/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_modern_theme_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/modern-theme.css */ \"(rsc)/./src/styles/modern-theme.css\");\n/* harmony import */ var _components_layout_test_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/layout/test-layout */ \"(rsc)/./src/components/layout/test-layout.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"实验管理系统\",\n    description: '基于\"实验即契约\"理念的科研实验管理平台'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_test_layout__WEBPACK_IMPORTED_MODULE_3__.TestLayout, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUpnQjtBQUNhO0FBQzBCO0FBSXRELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0Msc0VBQVVBOzBCQUNSSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgJy4uL3N0eWxlcy9tb2Rlcm4tdGhlbWUuY3NzJ1xuaW1wb3J0IHsgVGVzdExheW91dCB9IGZyb20gJy4uL2NvbXBvbmVudHMvbGF5b3V0L3Rlc3QtbGF5b3V0J1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAn5a6e6aqM566h55CG57O757ufJyxcbiAgZGVzY3JpcHRpb246ICfln7rkuo5cIuWunumqjOWNs+Wlkee6plwi55CG5b+155qE56eR56CU5a6e6aqM566h55CG5bmz5Y+wJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxUZXN0TGF5b3V0PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9UZXN0TGF5b3V0PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwiVGVzdExheW91dCIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/test-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/test-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TestLayout: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx#TestLayout`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fexperiments%2Fnew%2Fpage&page=%2Fexperiments%2Fnew%2Fpage&appPaths=%2Fexperiments%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fexperiments%2Fnew%2Fpage.tsx&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();